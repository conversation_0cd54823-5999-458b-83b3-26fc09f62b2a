# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

app-example

# Editor files
.vscode/
.idea/

# iOS specific
ios/Pods/
ios/build/
ios/Podfile.lock
ios/*.xcworkspace
ios/*.xcodeproj/xcuserdata/
ios/*.xcodeproj/project.xcworkspace/xcuserdata/

# Android specific
android/.gradle/
android/build/
android/local.properties
android/captures/
android/app/release/
android/keystores/
android/app/debug/
android/app/build/
android/gradle/caches/
android/gradle/daemon/
android/gradle/native/
android/gradle/wrapper/dists/
android/**/generated/
*.iml
*.aab
*.apk
*.hprof

.vscode/

backend/

# Environment
.env