{"expo": {"name": "fhir-mobile-mvp", "slug": "fhir-mobile-mvp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": ["fhirmobilemvp", "localhost", "127.0.0.1"], "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.anonymous.fhir-mobile-mvp"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.anonymous.fhirmobilemvp"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-secure-store"], "experiments": {"typedRoutes": true}}}