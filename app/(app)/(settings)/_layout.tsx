import { Slot } from "expo-router";
import { View, StyleSheet } from "react-native";

import { ThemeColors, useTheme } from "@/context/ThemeContext";

const SettingsLayout = () => {
   const { colors } = useTheme();
   const styles = getStyles(colors);

   return (
      <View style={styles.container}>
         <Slot />
      </View>
   );
};

const getStyles = (colors: ThemeColors) =>
   StyleSheet.create({
      container: {
         flex: 1,
         backgroundColor: colors.background,
      },
   });

export default SettingsLayout;
