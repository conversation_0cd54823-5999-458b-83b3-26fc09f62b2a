import React, { useState } from "react";
import {
   View,
   Text,
   TextInput,
   SafeAreaView,
   KeyboardAvoidingView,
   Platform,
   Pressable,
   ActivityIndicator,
   ScrollView,
   Alert,
   StyleSheet,
} from "react-native";

import { useAuth } from "@/context/AuthContext";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { Feather } from "@expo/vector-icons";
import ToastMessage from "@/components/ui/ToastMessage";
import { Colors } from "@/constants/colors";
import { getCommonStyles } from "@/constants/styles";
import { useRouter } from "expo-router";
import { useTheme } from "@/context/ThemeContext";
import useApi from "@/hooks/useApi";
import {
   usePasswordStrength,
   strengthColors,
   strengthWidths,
} from "@/hooks/usePasswordStrength";

const ChangePasswordScreen: React.FC = () => {
   const { signOut, accessToken } = useAuth();
   const { post } = useApi();
   const { colors } = useTheme();
   const styles = getStyles(colors);
   const commonStyles = getCommonStyles(colors);
   const router = useRouter();

   const [currentPassword, setCurrentPassword] = useState("");
   const [currentError, setCurrentError] = useState<string | null>(null);
   const [newError, setNewError] = useState<string | null>(null);
   const [apiError, setApiError] = useState<string | null>(null);
   const [toastVisible, setToastVisible] = useState(false);
   const [toastMessage, setToastMessage] = useState("");
   const [toastType, setToastType] = useState<"success" | "error">("error");
   const [showCurrentPassword, setShowCurrentPassword] = useState(false);
   const [isLoading, setIsLoading] = useState(false);

   const {
      password: newPassword,
      handlePasswordChange,
      passwordStrength,
      isPasswordFocused,
      setIsPasswordFocused,
      showPassword: showNewPassword,
      setShowPassword: setShowNewPassword,
      passwordRequirements,
   } = usePasswordStrength();

   const validateForm = () => {
      let valid = true;
      setCurrentError(null);
      setNewError(null);
      setApiError(null);
      setToastVisible(false);

      if (!currentPassword.trim()) {
         setToastMessage("Current password is required");
         setToastVisible(true);
         valid = false;
      }
      if (!newPassword.trim()) {
         setToastMessage("New password is required");
         setToastVisible(true);
         valid = false;
      } else if (newPassword.length < 8) {
         setToastMessage("New password must be at least 8 characters");
         setToastVisible(true);
         valid = false;
      }

      return valid;
   };

   const handleChange = async () => {
      if (!validateForm()) return;

      setIsLoading(true);
      try {
         if (!accessToken) {
            setToastMessage("Authentication token is missing");
            setToastVisible(true);
            return;
         }

         await post("/change-password", {
            data: {
               current_password: currentPassword,
               new_password: newPassword,
            },
            token: accessToken,
         });

         setToastMessage(
            "Password changed successfully. Please login with your new password.",
         );
         setToastType("success");
         setToastVisible(true);

         setCurrentPassword("");
         setTimeout(async () => {
            setToastVisible(false);
            await signOut();
            router.replace("/login");
         }, 2000);
      } catch (error: any) {
         let message;
         if (error instanceof Error) {
            message = error.message;
            if (error.message.includes("Failed to refresh token")) {
               message = "Your session has expired";
               await signOut();
            }
         } else message = "Unexpected error";

         setApiError(message);
         setToastMessage("Failed to change password");
         setToastVisible(true);
         Alert.alert("Couldn't change password", message);
      } finally {
         setIsLoading(false);
      }
   };

   return (
      <SafeAreaView style={commonStyles.safeArea}>
         <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={commonStyles.keyboardAvoidingView}
         >
            <ToastMessage
               visible={toastVisible}
               message={toastMessage}
               onHide={() => setToastVisible(false)}
               type={toastType}
            />
            <ScrollView
               contentContainerStyle={commonStyles.scrollViewContent}
               keyboardShouldPersistTaps="handled"
            >
               <View style={commonStyles.formContainer}>
                  <Text style={commonStyles.title}>Change Password</Text>
                  {apiError && (
                     <Text
                        style={[
                           commonStyles.errorText,
                           commonStyles.apiErrorText,
                        ]}
                     >
                        {apiError}
                     </Text>
                  )}

                  <View style={commonStyles.inputGroup}>
                     <MaterialIcons
                        name="lock"
                        size={20}
                        color={colors.icon}
                        style={commonStyles.inputIcon}
                     />
                     <TextInput
                        style={commonStyles.input}
                        placeholder="Current Password"
                        placeholderTextColor={colors.icon}
                        secureTextEntry={!showCurrentPassword}
                        value={currentPassword}
                        onChangeText={setCurrentPassword}
                        textContentType="password"
                        autoComplete="password"
                     />
                     <Pressable
                        onPress={() =>
                           setShowCurrentPassword(!showCurrentPassword)
                        }
                        style={commonStyles.eyeButton}
                     >
                        <MaterialIcons
                           name={
                              showCurrentPassword
                                 ? "visibility-off"
                                 : "visibility"
                           }
                           size={20}
                           color={colors.icon}
                        />
                     </Pressable>
                  </View>
                  {currentError && (
                     <Text style={commonStyles.errorText}>{currentError}</Text>
                  )}

                  <View style={commonStyles.inputGroup}>
                     <MaterialIcons
                        name="lock-outline"
                        size={20}
                        color={colors.icon}
                        style={commonStyles.inputIcon}
                     />
                     <TextInput
                        style={commonStyles.input}
                        placeholder="New Password"
                        placeholderTextColor={colors.icon}
                        secureTextEntry={!showNewPassword}
                        value={newPassword}
                        onChangeText={handlePasswordChange}
                        textContentType="newPassword"
                        autoComplete="password-new"
                        onFocus={() => setIsPasswordFocused(true)}
                        onBlur={() => setIsPasswordFocused(false)}
                     />
                     <Pressable
                        onPress={() => setShowNewPassword(!showNewPassword)}
                        style={commonStyles.eyeButton}
                     >
                        <MaterialIcons
                           name={
                              showNewPassword ? "visibility-off" : "visibility"
                           }
                           size={20}
                           color={colors.icon}
                        />
                     </Pressable>
                  </View>
                  {newError && (
                     <Text style={commonStyles.errorText}>{newError}</Text>
                  )}

                  {isPasswordFocused && (
                     <View
                        style={{
                           marginTop: 2,
                           marginBottom: 4,
                           paddingHorizontal: 14,
                        }}
                     >
                        {passwordRequirements.map((req, i) => {
                           const passed = req.test(newPassword);
                           return (
                              <View
                                 key={i}
                                 style={{
                                    flexDirection: "row",
                                    alignItems: "center",
                                    marginBottom: 2,
                                 }}
                              >
                                 <Feather
                                    name={passed ? "check-circle" : "circle"}
                                    size={16}
                                    color={passed ? "#5cb85c" : "#bbb"}
                                    style={{ marginRight: 6 }}
                                 />
                                 <Text
                                    style={{
                                       fontSize: 13,
                                       color: passed ? "#5cb85c" : "#888",
                                       textDecorationLine: passed
                                          ? "none"
                                          : "none",
                                    }}
                                 >
                                    {req.label}
                                 </Text>
                              </View>
                           );
                        })}
                     </View>
                  )}

                  {passwordStrength !== "" && (
                     <View style={styles.strengthWrapper}>
                        <View style={styles.strengthTrack}>
                           <View
                              style={[
                                 styles.strengthFill,
                                 {
                                    backgroundColor:
                                       strengthColors[passwordStrength],
                                    width: strengthWidths[passwordStrength],
                                 },
                              ]}
                           />
                        </View>
                        <Text style={styles.strengthLabel}>
                           {passwordStrength.charAt(0).toUpperCase() +
                              passwordStrength.slice(1)}
                        </Text>
                     </View>
                  )}

                  <Pressable
                     style={({ pressed }) => [
                        commonStyles.button,
                        pressed && commonStyles.buttonPressed,
                        isLoading && commonStyles.buttonDisabled,
                     ]}
                     onPress={handleChange}
                     disabled={isLoading}
                  >
                     {isLoading ? (
                        <ActivityIndicator color={Colors.dark.text} />
                     ) : (
                        <Text style={commonStyles.buttonText}>Submit</Text>
                     )}
                  </Pressable>
                  <Pressable
                     style={({ pressed }) => [
                        commonStyles.cancelButton,
                        pressed && commonStyles.cancelButtonPressed,
                     ]}
                     onPress={() => {
                        router.back();
                     }}
                  >
                     <Text style={commonStyles.cancelButtonText}>Cancel</Text>
                  </Pressable>
               </View>
            </ScrollView>
         </KeyboardAvoidingView>
      </SafeAreaView>
   );
};

const getStyles = (colors: any) =>
   StyleSheet.create({
      strengthWrapper: {
         marginTop: 4,
         marginBottom: 10,
         paddingHorizontal: 12,
      },
      strengthTrack: {
         width: "100%",
         height: 6,
         borderRadius: 4,
         backgroundColor: "#ddd",
         overflow: "hidden",
      },
      strengthFill: {
         height: "100%",
         borderRadius: 4,
         width: "0%",
      },
      strengthLabel: {
         marginTop: 4,
         fontSize: 12,
         color: "#888",
      },
   });

export default ChangePasswordScreen;
