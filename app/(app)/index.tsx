import React, { useEffect, useState } from "react";
import { ScrollView, Alert, Platform, PermissionsAndroid } from "react-native";
import { Pressable } from "react-native";
import { Link } from "expo-router";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import Header from "@/components/ui/Header";
import { useIHealth } from "@/hooks/useIHealth";
import iHealthService, { DeviceInfo } from "@/service/iHealthService";

export default function HomeScreen() {
   const { isSDKAvailable } = useIHealth();

   const [isScanning, setIsScanning] = useState(false);
   const [discoveredDevices, setDiscoveredDevices] = useState<DeviceInfo[]>([]);
   const [connectedDevice, setConnectedDevice] = useState<DeviceInfo | null>(
      null,
   );
   const [connectionStatus, setConnectionStatus] =
      useState<string>("disconnected");
   const [logs, setLogs] = useState<string[]>([]);
   const [scanTimer, setScanTimer] = useState<number>(0);

   // Add log function with console output
   const addLog = (message: string) => {
      const timestamp = new Date().toLocaleTimeString();
      const logMessage = `[${timestamp}] ${message}`;
      console.log("📱 iHealth App:", logMessage); // Console output for debugging
      setLogs((prev) => [...prev.slice(-19), logMessage]); // Keep more logs
   };

   // Scan timer effect
   useEffect(() => {
      let interval: ReturnType<typeof setInterval>;
      if (isScanning && scanTimer > 0) {
         interval = setInterval(() => {
            setScanTimer((prev) => {
               if (prev <= 1) {
                  setIsScanning(false);
                  addLog("⏰ Scan timeout - stopping scan");
                  iHealthService.stopScan();
                  return 0;
               }
               return prev - 1;
            });
         }, 1000);
      }
      return () => {
         if (interval) clearInterval(interval);
      };
   }, [isScanning, scanTimer]);

   useEffect(() => {
      if (!isSDKAvailable) {
         addLog("❌ iHealth SDK not available");
         return;
      }

      addLog("✅ iHealth SDK available - Ready for device scanning");
      addLog("⚠️  IMPORTANT: Close iHealth MyVitals app if it's running!");

      // Setup event listeners
      const setupListeners = () => {
         iHealthService.addEventListener(
            "deviceDiscovered",
            (device: DeviceInfo) => {
               addLog(
                  `🔍 Device discovered: ${device.deviceName || "Unknown"} (${device.mac})`,
               );
               addLog(
                  `📋 Device type: ${device.deviceType}, Protocol: ${device.protocolString}`,
               );
               setDiscoveredDevices((prev) => {
                  // Avoid duplicates
                  if (prev.find((d) => d.mac === device.mac)) return prev;
                  return [...prev, device];
               });
            },
         );

         iHealthService.addEventListener(
            "deviceConnected",
            (device: DeviceInfo) => {
               addLog(`✅ Successfully connected to: ${device.deviceName}`);
               setConnectedDevice(device);
               setConnectionStatus("connected");
               setIsScanning(false);
               setScanTimer(0);
            },
         );

         iHealthService.addEventListener("deviceError", (error: any) => {
            addLog(`❌ Connection error: ${JSON.stringify(error)}`);
            setConnectionStatus("error");
         });

         iHealthService.addEventListener(
            "deviceDisconnected",
            (device: any) => {
               addLog(`🔌 Device disconnected`);
               setConnectedDevice(null);
               setConnectionStatus("disconnected");
            },
         );

         iHealthService.addEventListener(
            "measurementReceived",
            (measurement: any) => {
               addLog(
                  `📏 Measurement received: ${JSON.stringify(measurement)}`,
               );
               Alert.alert(
                  "Measurement Received!",
                  `Weight: ${measurement.weight || "N/A"} kg\nBMI: ${measurement.bmi || "N/A"}`,
                  [{ text: "OK" }],
               );
            },
         );
      };

      setupListeners();
      addLog("🎧 Event listeners configured");

      return () => {
         iHealthService.cleanup();
      };
   }, [isSDKAvailable]);

   const handleStartScan = async () => {
      try {
         // Clear previous results
         setDiscoveredDevices([]);
         setConnectionStatus("disconnected");

         addLog(
            "🚨 CRITICAL: Make sure iHealth MyVitals app is COMPLETELY CLOSED!",
         );
         addLog("📍 Requesting Bluetooth permissions...");

         if (Platform.OS === "android") {
            // For Android 12+ (API 31+)
            if (Platform.Version >= 31) {
               const permissions = await PermissionsAndroid.requestMultiple([
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
                  PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
               ]);

               if (
                  permissions[PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN] !==
                     PermissionsAndroid.RESULTS.GRANTED ||
                  permissions[
                     PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
                  ] !== PermissionsAndroid.RESULTS.GRANTED ||
                  permissions[
                     PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
                  ] !== PermissionsAndroid.RESULTS.GRANTED
               ) {
                  addLog(
                     "❌ Required Bluetooth & Location permissions denied.",
                  );
                  Alert.alert(
                     "Permission Required",
                     "Bluetooth and Location permissions are required for device discovery.",
                  );
                  return;
               }
            } else {
               // For Android 11 and below
               const result = await PermissionsAndroid.request(
                  PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
               );
               if (result !== PermissionsAndroid.RESULTS.GRANTED) {
                  addLog("❌ Location permission denied");
                  Alert.alert(
                     "Permission Required",
                     "Location permission is required for device discovery.",
                  );
                  return;
               }
            }
            addLog("✅ Android permissions granted.");
         } else {
            addLog(
               "✅ iOS - Bluetooth permissions should be handled automatically.",
            );
         }

         // Start scanning with timer
         setIsScanning(true);
         setScanTimer(60); // 60 second countdown
         addLog("🎯 OFFICIAL: Starting PO3M pulse oximeter discovery...");
         addLog("🫁 TURN ON YOUR PO3M PULSE OXIMETER NOW!");
         addLog("🔧 Using official native implementation...");

         try {
            await iHealthService.startPO3MDiscoveryOfficial();
         } catch (officialError) {
            addLog(`❌ Official method failed: ${officialError}`);
            addLog("🔄 Trying direct Bluetooth scan...");
            try {
               await iHealthService.tryDirectBluetoothScan();
            } catch (directError) {
               addLog(`❌ Direct Bluetooth failed: ${directError}`);
               addLog("🔄 Falling back to comprehensive scan...");
               await iHealthService.startScan();
            }
         }
      } catch (error) {
         console.error("❌ Scan error details:", error);
         addLog(`❌ Scan failed: ${error}`);
         Alert.alert("Scan Error", `Failed to start scan: ${error}`);
         setIsScanning(false);
         setScanTimer(0);
      }
   };

   const handleStopScan = async () => {
      try {
         setIsScanning(false);
         setScanTimer(0);
         await iHealthService.stopScan();
         addLog("🛑 Scan stopped manually");
      } catch (error) {
         addLog(`❌ Stop scan failed: ${error}`);
      }
   };

   const handleConnectDevice = async (device: DeviceInfo) => {
      try {
         addLog(
            `🔗 Attempting to connect to ${device.deviceName || "iHealth Device"}...`,
         );
         addLog(
            `📋 Device details - MAC: ${device.mac}, Type: ${device.deviceType}`,
         );
         addLog(
            `🚨 CRITICAL: Make sure iHealth MyVitals app is COMPLETELY CLOSED!`,
         );
         setConnectionStatus("connecting");

         // Pass the device type to the connection method
         const success = await iHealthService.connectDevice(
            device.mac,
            device.deviceType,
         );
         if (success) {
            addLog(
               "✅ Connection request sent - waiting for device response...",
            );
            addLog("⏳ This may take 10-30 seconds...");
            addLog(
               "🔋 Make sure your PO3M pulse oximeter is powered on and ready",
            );
         } else {
            addLog("❌ Failed to initiate connection");
            setConnectionStatus("error");
         }
      } catch (error) {
         addLog(`❌ Connection failed: ${error}`);
         setConnectionStatus("error");
         Alert.alert("Connection Error", `Failed to connect: ${error}`);
      }
   };

   const handleDisconnectDevice = async () => {
      try {
         addLog("🔌 Disconnecting device...");
         await iHealthService.disconnectDevice();
         addLog("✅ Disconnection initiated");
      } catch (error) {
         addLog(`❌ Disconnect failed: ${error}`);
      }
   };

   const handleTestDevice = async () => {
      try {
         addLog("🧪 Initiating device test...");
         await iHealthService.testDeviceFunctions();
         addLog("✅ Device test started - USE YOUR PO3M PULSE OXIMETER NOW!");
         Alert.alert(
            "Device Test Started",
            "Use your PO3M pulse oximeter now to trigger a measurement!\n\nThe device should display your SpO2 and pulse rate.",
            [{ text: "OK" }],
         );
      } catch (error) {
         addLog(`❌ Device test failed: ${error}`);
         Alert.alert("Test Error", `Device test failed: ${error}`);
      }
   };

   const clearLogs = () => {
      setLogs([]);
      addLog("🧹 Logs cleared");
   };

   if (!isSDKAvailable) {
      return (
         <ThemedView style={{ flex: 1 }}>
            <Header />
            <ScrollView style={{ flex: 1, padding: 20 }}>
               <ThemedText
                  style={{
                     fontSize: 18,
                     marginBottom: 20,
                     textAlign: "center",
                  }}
               >
                  ❌ iHealth SDK Not Available
               </ThemedText>
               <ThemedText style={{ textAlign: "center", color: "#666" }}>
                  The iHealth library is not properly installed. Please check
                  the installation.
               </ThemedText>
            </ScrollView>
         </ThemedView>
      );
   }

   return (
      <ThemedView style={{ flex: 1 }}>
         <Header />
         <ScrollView style={{ flex: 1, padding: 20 }}>
            <ThemedText
               style={{
                  fontSize: 24,
                  fontWeight: "bold",
                  marginBottom: 10,
                  textAlign: "center",
               }}
            >
               🩺 iHealth Device Scanner
            </ThemedText>

            <ThemedText
               style={{
                  fontSize: 14,
                  marginBottom: 20,
                  textAlign: "center",
                  color: "#666",
               }}
            >
               🎯 OFFICIAL: Test PO3M Pulse Oximeter Connection
            </ThemedText>

            {/* Critical Warning */}
            <ThemedView
               style={{
                  backgroundColor: "#fff3cd",
                  padding: 15,
                  borderRadius: 8,
                  marginBottom: 20,
                  borderLeftWidth: 4,
                  borderLeftColor: "#ffc107",
               }}
            >
               <ThemedText
                  style={{
                     fontSize: 16,
                     fontWeight: "bold",
                     marginBottom: 10,
                     color: "#856404",
                  }}
               >
                  ⚠️ IMPORTANT BEFORE SCANNING:
               </ThemedText>
               <ThemedText
                  style={{ fontSize: 14, color: "#856404", lineHeight: 20 }}
               >
                  1. CLOSE the iHealth MyVitals app completely{"\n"}
                  2. Make sure your PO3M pulse oximeter is nearby{"\n"}
                  3. Be ready to TURN ON the pulse oximeter when scanning starts
               </ThemedText>
            </ThemedView>

            {/* Connection Status */}
            <ThemedView
               style={{
                  backgroundColor:
                     connectionStatus === "connected"
                        ? "#d4edda"
                        : connectionStatus === "connecting"
                          ? "#fff3cd"
                          : connectionStatus === "error"
                            ? "#f8d7da"
                            : "#e2e3e5",
                  padding: 15,
                  borderRadius: 8,
                  marginBottom: 20,
                  borderLeftWidth: 4,
                  borderLeftColor:
                     connectionStatus === "connected"
                        ? "#28a745"
                        : connectionStatus === "connecting"
                          ? "#ffc107"
                          : connectionStatus === "error"
                            ? "#dc3545"
                            : "#6c757d",
               }}
            >
               <ThemedText
                  style={{
                     fontSize: 18,
                     fontWeight: "bold",
                     textAlign: "center",
                  }}
               >
                  {connectionStatus === "connected"
                     ? "✅ CONNECTED"
                     : connectionStatus === "connecting"
                       ? "🔄 CONNECTING..."
                       : connectionStatus === "error"
                         ? "❌ ERROR"
                         : "⚪ READY TO SCAN"}
               </ThemedText>
               {connectedDevice && (
                  <ThemedText
                     style={{ textAlign: "center", marginTop: 5, fontSize: 14 }}
                  >
                     {connectedDevice.deviceName} ({connectedDevice.mac})
                  </ThemedText>
               )}
               {isScanning && scanTimer > 0 && (
                  <ThemedText
                     style={{
                        textAlign: "center",
                        marginTop: 5,
                        fontSize: 14,
                        fontWeight: "bold",
                     }}
                  >
                     ⏱️ Scanning... {scanTimer}s remaining
                  </ThemedText>
               )}
            </ThemedView>

            {/* Main Control Button */}
            <Pressable
               style={{
                  backgroundColor: isScanning
                     ? "#dc3545"
                     : connectedDevice
                       ? "#28a745"
                       : "#007bff",
                  padding: 20,
                  borderRadius: 10,
                  marginBottom: 20,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.25,
                  shadowRadius: 3.84,
                  elevation: 5,
               }}
               onPress={
                  isScanning
                     ? handleStopScan
                     : connectedDevice
                       ? handleTestDevice
                       : handleStartScan
               }
            >
               <ThemedText
                  style={{
                     color: "white",
                     textAlign: "center",
                     fontSize: 18,
                     fontWeight: "bold",
                  }}
               >
                  {isScanning
                     ? "🛑 STOP SCANNING"
                     : connectedDevice
                       ? "🧪 TEST DEVICE (Use Pulse Oximeter)"
                       : "🎯 OFFICIAL: Start PO3M Discovery"}
               </ThemedText>
            </Pressable>

            {/* Discovered Devices */}
            {discoveredDevices.length > 0 && (
               <ThemedView style={{ marginBottom: 20 }}>
                  <ThemedText
                     style={{
                        fontSize: 16,
                        fontWeight: "bold",
                        marginBottom: 15,
                        textAlign: "center",
                     }}
                  >
                     🔍 Found {discoveredDevices.length} Device(s)
                  </ThemedText>
                  {discoveredDevices.map((device, index) => (
                     <ThemedView
                        key={index}
                        style={{
                           backgroundColor: "white",
                           padding: 15,
                           borderRadius: 8,
                           marginBottom: 10,
                           borderLeftWidth: 4,
                           borderLeftColor: "#007bff",
                           shadowColor: "#000",
                           shadowOffset: { width: 0, height: 1 },
                           shadowOpacity: 0.22,
                           shadowRadius: 2.22,
                           elevation: 3,
                        }}
                     >
                        <ThemedText
                           style={{
                              fontSize: 16,
                              fontWeight: "bold",
                              marginBottom: 5,
                           }}
                        >
                           📱{" "}
                           {device.deviceName || "iHealth PO3M Pulse Oximeter"}
                        </ThemedText>
                        <ThemedText
                           style={{
                              fontSize: 12,
                              color: "#666",
                              marginBottom: 2,
                           }}
                        >
                           MAC: {device.mac}
                        </ThemedText>
                        <ThemedText
                           style={{
                              fontSize: 12,
                              color: "#666",
                              marginBottom: 10,
                           }}
                        >
                           Type: {device.deviceType}
                        </ThemedText>

                        {connectionStatus !== "connected" &&
                           connectionStatus !== "connecting" && (
                              <Pressable
                                 style={{
                                    backgroundColor: "#28a745",
                                    padding: 12,
                                    borderRadius: 6,
                                    marginTop: 8,
                                 }}
                                 onPress={() => handleConnectDevice(device)}
                              >
                                 <ThemedText
                                    style={{
                                       color: "white",
                                       textAlign: "center",
                                       fontSize: 14,
                                       fontWeight: "bold",
                                    }}
                                 >
                                    🔗 CONNECT TO THIS DEVICE
                                 </ThemedText>
                              </Pressable>
                           )}
                     </ThemedView>
                  ))}
               </ThemedView>
            )}

            {/* Connected Device Controls */}
            {connectedDevice && (
               <ThemedView style={{ marginBottom: 20 }}>
                  <ThemedView
                     style={{
                        backgroundColor: "#d4edda",
                        padding: 15,
                        borderRadius: 8,
                        borderLeftWidth: 4,
                        borderLeftColor: "#28a745",
                        marginBottom: 15,
                     }}
                  >
                     <ThemedText
                        style={{
                           fontSize: 16,
                           fontWeight: "bold",
                           marginBottom: 5,
                        }}
                     >
                        ✅ Connected Device:
                     </ThemedText>
                     <ThemedText style={{ fontSize: 14, marginBottom: 2 }}>
                        📱 {connectedDevice.deviceName}
                     </ThemedText>
                     <ThemedText style={{ fontSize: 12, color: "#666" }}>
                        MAC: {connectedDevice.mac}
                     </ThemedText>
                  </ThemedView>

                  <Pressable
                     style={{
                        backgroundColor: "#dc3545",
                        padding: 12,
                        borderRadius: 8,
                        marginBottom: 10,
                     }}
                     onPress={handleDisconnectDevice}
                  >
                     <ThemedText
                        style={{
                           color: "white",
                           textAlign: "center",
                           fontSize: 14,
                           fontWeight: "bold",
                        }}
                     >
                        🔌 DISCONNECT DEVICE
                     </ThemedText>
                  </Pressable>
               </ThemedView>
            )}

            {/* Activity Logs */}
            <ThemedView style={{ marginBottom: 20 }}>
               <ThemedView
                  style={{
                     flexDirection: "row",
                     justifyContent: "space-between",
                     alignItems: "center",
                     marginBottom: 10,
                  }}
               >
                  <ThemedText style={{ fontSize: 16, fontWeight: "bold" }}>
                     📋 Console Logs
                  </ThemedText>
                  <Pressable
                     style={{
                        backgroundColor: "#6c757d",
                        paddingHorizontal: 12,
                        paddingVertical: 6,
                        borderRadius: 4,
                     }}
                     onPress={clearLogs}
                  >
                     <ThemedText style={{ color: "white", fontSize: 12 }}>
                        Clear
                     </ThemedText>
                  </Pressable>
               </ThemedView>
               <ThemedView
                  style={{
                     backgroundColor: "#000",
                     padding: 12,
                     borderRadius: 8,
                     maxHeight: 300,
                  }}
               >
                  <ScrollView style={{ maxHeight: 280 }}>
                     {logs.length === 0 ? (
                        <ThemedText
                           style={{
                              color: "#888",
                              fontStyle: "italic",
                              fontFamily: "monospace",
                           }}
                        >
                           Waiting for activity...
                        </ThemedText>
                     ) : (
                        logs.map((log, index) => (
                           <ThemedText
                              key={index}
                              style={{
                                 fontSize: 11,
                                 marginBottom: 3,
                                 color: "#00ff00",
                                 fontFamily: "monospace",
                              }}
                           >
                              {log}
                           </ThemedText>
                        ))
                     )}
                  </ScrollView>
               </ThemedView>
            </ThemedView>

            {/* Quick Navigation */}
            <ThemedView style={{ gap: 10, marginTop: 20 }}>
               <ThemedText
                  style={{
                     fontSize: 14,
                     fontWeight: "bold",
                     textAlign: "center",
                     color: "#666",
                     marginBottom: 10,
                  }}
               >
                  Other App Features
               </ThemedText>
               <ThemedView style={{ flexDirection: "row", gap: 10 }}>
                  <Link href="/profile" asChild>
                     <Pressable
                        style={{
                           backgroundColor: "#6c757d",
                           padding: 10,
                           borderRadius: 6,
                           alignItems: "center",
                           flex: 1,
                        }}
                     >
                        <ThemedText
                           style={{
                              color: "white",
                              fontSize: 12,
                              fontWeight: "bold",
                           }}
                        >
                           👤 Profile
                        </ThemedText>
                     </Pressable>
                  </Link>

                  <Link href="/devices" asChild>
                     <Pressable
                        style={{
                           backgroundColor: "#6c757d",
                           padding: 10,
                           borderRadius: 6,
                           alignItems: "center",
                           flex: 1,
                        }}
                     >
                        <ThemedText
                           style={{
                              color: "white",
                              fontSize: 12,
                              fontWeight: "bold",
                           }}
                        >
                           📱 Devices
                        </ThemedText>
                     </Pressable>
                  </Link>

                  <Link href="/reading-list" asChild>
                     <Pressable
                        style={{
                           backgroundColor: "#6c757d",
                           padding: 10,
                           borderRadius: 6,
                           alignItems: "center",
                           flex: 1,
                        }}
                     >
                        <ThemedText
                           style={{
                              color: "white",
                              fontSize: 12,
                              fontWeight: "bold",
                           }}
                        >
                           📊 Readings
                        </ThemedText>
                     </Pressable>
                  </Link>
               </ThemedView>
            </ThemedView>
         </ScrollView>
      </ThemedView>
   );
}
