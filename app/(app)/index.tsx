import React, { useEffect, useState } from "react";
import { View, ScrollView, Alert, Platform, PermissionsAndroid } from "react-native";
import { Pressable } from "react-native";
import { Link } from "expo-router";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import Header from "@/components/ui/Header";
import { useIHealth } from "@/hooks/useIHealth";
import iHealthService, { DeviceInfo } from "@/service/iHealthService";

export default function HomeScreen() {
   const { isSDKAvailable, requestPermissions } = useIHealth();

   const [isScanning, setIsScanning] = useState(false);
   const [discoveredDevices, setDiscoveredDevices] = useState<DeviceInfo[]>(
      [],
   );
   const [connectedDevice, setConnectedDevice] = useState<DeviceInfo | null>(
      null,
   );
   const [connectionStatus, setConnectionStatus] =
      useState<string>("disconnected");
   const [logs, setLogs] = useState<string[]>([]);

   // Add log function
   const addLog = (message: string) => {
      const timestamp = new Date().toLocaleTimeString();
      setLogs((prev) => [...prev.slice(-9), `[${timestamp}] ${message}`]);
   };

   useEffect(() => {
      if (!isSDKAvailable) {
         addLog("❌ iHealth SDK not available");
         return;
      }

      addLog("✅ iHealth SDK available");

      // Setup event listeners
      const setupListeners = () => {
         iHealthService.addEventListener(
            "deviceDiscovered",
            (device: DeviceInfo) => {
               addLog(`🔍 Device found: ${device.deviceName} (${device.mac})`);
               setDiscoveredDevices((prev) => {
                  // Avoid duplicates
                  if (prev.find((d) => d.mac === device.mac)) return prev;
                  return [...prev, device];
               });
            },
         );

         iHealthService.addEventListener(
            "deviceConnected",
            (device: DeviceInfo) => {
               addLog(`✅ Connected to: ${device.deviceName}`);
               setConnectedDevice(device);
               setConnectionStatus("connected");
            },
         );

         iHealthService.addEventListener("deviceError", (error: any) => {
            addLog(`❌ Connection error: ${JSON.stringify(error)}`);
            setConnectionStatus("error");
         });

         iHealthService.addEventListener(
            "deviceDisconnected",
            (device: any) => {
               addLog(`🔌 Disconnected from device`);
               setConnectedDevice(null);
               setConnectionStatus("disconnected");
            },
         );

         iHealthService.addEventListener(
            "measurementReceived",
            (measurement: any) => {
               addLog(`📏 Measurement: ${JSON.stringify(measurement)}`);
               Alert.alert(
                  "Measurement Received!",
                  `Weight: ${measurement.weight || "N/A"} kg\nBMI: ${measurement.bmi || "N/A"}`,
                  [{ text: "OK" }],
               );
            },
         );
      };

      setupListeners();
      addLog("🎧 Event listeners setup complete");

      return () => {
         iHealthService.cleanup();
      };
   }, [isSDKAvailable]);

   const handleStartScan = async () => {
      try {
         // Step 1: Directly request all necessary permissions for Android
         addLog("📍 Requesting permissions...");
         if (Platform.OS === "android") {
            // For Android 12+ (API 31+)
            if (Platform.Version >= 31) {
               const permissions = await PermissionsAndroid.requestMultiple([
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
                  PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
               ]);

               if (
                  permissions[PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN] !== PermissionsAndroid.RESULTS.GRANTED ||
                  permissions[PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT] !== PermissionsAndroid.RESULTS.GRANTED ||
                  permissions[PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION] !== PermissionsAndroid.RESULTS.GRANTED
               ) {
                  addLog("❌ Required Bluetooth & Location permissions denied.");
                  Alert.alert("Permission Required", "Bluetooth and Location permissions are required for device discovery.");
                  return;
               }
            } else { // For Android 11 and below
               const result = await PermissionsAndroid.request(
                  PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
               );
               if (result !== PermissionsAndroid.RESULTS.GRANTED) {
                  addLog("❌ Location permission denied");
                  Alert.alert("Permission Required", "Location permission is required for device discovery.");
                  return;
               }
            }
            addLog("✅ Permissions granted.");
         }

         // Step 2: Start the scan
         setIsScanning(true);
         setDiscoveredDevices([]);
         addLog("🔍 Starting device scan - step on your scale!");
         await iHealthService.startScan();
      } catch (error) {
         console.error("Scan error details:", error);
         addLog(`❌ Scan failed: ${error}`);
         Alert.alert("Scan Error", `Failed to start scan: ${error}`);
      } finally {
         // Set a longer timeout since discovery runs for 60 seconds
         setTimeout(() => {
            setIsScanning(false);
         }, 61000);
      }
   };

   const handleStopScan = async () => {
      try {
         setIsScanning(false);
         await iHealthService.stopScan();
         addLog("🛑 Scan stopped");
      } catch (error) {
         addLog(`❌ Stop scan failed: ${error}`);
      }
   };

   const handleConnectDevice = async (device: DeviceInfo) => {
      try {
         addLog(`🔗 Connecting to ${device.deviceName}...`);
         setConnectionStatus("connecting");
         const success = await iHealthService.connectDevice(device.mac);
         if (success) {
            addLog("✅ Connection initiated - waiting for response...");
         } else {
            addLog("❌ Failed to initiate connection");
            setConnectionStatus("error");
         }
      } catch (error) {
         addLog(`❌ Connection failed: ${error}`);
         setConnectionStatus("error");
         Alert.alert("Connection Error", `Failed to connect: ${error}`);
      }
   };

   const handleDisconnectDevice = async () => {
      try {
         await iHealthService.disconnectDevice();
         addLog("🔌 Disconnection initiated");
      } catch (error) {
         addLog(`❌ Disconnect failed: ${error}`);
      }
   };

   const handleTestDevice = async () => {
      try {
         addLog("🧪 Testing device functions...");
         await iHealthService.testDeviceFunctions();
         addLog("✅ Device test initiated - step on scale for measurements");
         Alert.alert(
            "Device Test Started",
            "Step on your scale now to trigger a measurement!",
            [{ text: "OK" }],
         );
      } catch (error) {
         addLog(`❌ Device test failed: ${error}`);
         Alert.alert("Test Error", `Device test failed: ${error}`);
      }
   };

   if (!isSDKAvailable) {
      return (
         <ThemedView style={{ flex: 1 }}>
            <Header />
            <ScrollView style={{ flex: 1, padding: 20 }}>
               <ThemedText
                  style={{
                     fontSize: 18,
                     marginBottom: 20,
                     textAlign: "center",
                  }}
               >
                  ❌ iHealth SDK Not Available
               </ThemedText>
               <ThemedText style={{ textAlign: "center", color: "#666" }}>
                  The iHealth library is not properly installed. Please check
                  the installation.
               </ThemedText>
            </ScrollView>
         </ThemedView>
      );
   }

   return (
      <ThemedView style={{ flex: 1 }}>
         <Header />
         <ScrollView style={{ flex: 1, padding: 20 }}>
            <ThemedText
               style={{
                  fontSize: 24,
                  fontWeight: "bold",
                  marginBottom: 20,
                  textAlign: "center",
               }}
            >
               🏥 Welcome to FHIR Mobile MVP
            </ThemedText>

            {/* iHealth Device Testing Section */}
            <ThemedView style={{ 
               backgroundColor: "#f5f5f5", 
               padding: 15, 
               borderRadius: 8, 
               marginBottom: 20 
            }}>
               <ThemedText style={{ fontSize: 18, fontWeight: "bold", marginBottom: 15 }}>
                  📊 iHealth HS2S Pro Scale Testing
               </ThemedText>

               {/* Connection Status */}
               <ThemedView style={{ 
                  backgroundColor: connectionStatus === "connected" ? "#d4edda" : 
                                connectionStatus === "connecting" ? "#fff3cd" :
                                connectionStatus === "error" ? "#f8d7da" : "#e2e3e5",
                  padding: 10,
                  borderRadius: 5,
                  marginBottom: 15
               }}>
                  <ThemedText style={{ fontWeight: "bold", textAlign: "center" }}>
                     Status: {connectionStatus.toUpperCase()}
                     {connectedDevice && ` - ${connectedDevice.deviceName}`}
                  </ThemedText>
               </ThemedView>

               {/* Control Buttons */}
               <ThemedView style={{ flexDirection: "row", gap: 10, marginBottom: 15 }}>
                  <Pressable
                     style={{
                        backgroundColor: isScanning ? "#6c757d" : "#007bff",
                        padding: 10,
                        borderRadius: 5,
                        flex: 1,
                        opacity: isScanning ? 0.6 : 1
                     }}
                     onPress={isScanning ? handleStopScan : handleStartScan}
                     disabled={isScanning && false} // Allow stopping
                  >
                     <ThemedText style={{ color: "white", textAlign: "center", fontWeight: "bold" }}>
                        {isScanning ? "🛑 Stop Scan" : "🔍 Start Scan"}
                     </ThemedText>
                  </Pressable>

                  {connectedDevice && (
                     <Pressable
                        style={{
                           backgroundColor: "#28a745",
                           padding: 10,
                           borderRadius: 5,
                           flex: 1
                        }}
                        onPress={handleTestDevice}
                     >
                        <ThemedText style={{ color: "white", textAlign: "center", fontWeight: "bold" }}>
                           🧪 Test Device
                        </ThemedText>
                     </Pressable>
                  )}
               </ThemedView>

               {/* Discovered Devices */}
               {discoveredDevices.length > 0 && (
                  <ThemedView style={{ marginBottom: 15 }}>
                     <ThemedText style={{ fontWeight: "bold", marginBottom: 10 }}>
                        🔍 Discovered Devices ({discoveredDevices.length}):
                     </ThemedText>
                     {discoveredDevices.map((device, index) => (
                        <ThemedView 
                           key={index}
                           style={{ 
                              backgroundColor: "white", 
                              padding: 10, 
                              borderRadius: 5, 
                              marginBottom: 5,
                              borderLeftWidth: 3,
                              borderLeftColor: "#007bff"
                           }}
                        >
                           <ThemedText style={{ fontWeight: "bold" }}>
                              {device.deviceName || "iHealth Scale"}
                           </ThemedText>
                           <ThemedText style={{ fontSize: 12, color: "#666" }}>
                              MAC: {device.mac}
                           </ThemedText>
                           <ThemedText style={{ fontSize: 12, color: "#666" }}>
                              Type: {device.deviceType}
                           </ThemedText>
                           
                           {connectionStatus !== "connected" && (
                              <Pressable
                                 style={{
                                    backgroundColor: "#28a745",
                                    padding: 8,
                                    borderRadius: 3,
                                    marginTop: 8
                                 }}
                                 onPress={() => handleConnectDevice(device)}
                              >
                                 <ThemedText style={{ color: "white", textAlign: "center", fontSize: 12 }}>
                                    Connect
                                 </ThemedText>
                              </Pressable>
                           )}
                        </ThemedView>
                     ))}
                  </ThemedView>
               )}

               {/* Connected Device Controls */}
               {connectedDevice && (
                  <ThemedView style={{ marginBottom: 15 }}>
                     <ThemedText style={{ fontWeight: "bold", marginBottom: 10 }}>
                        ✅ Connected Device:
                     </ThemedText>
                     <ThemedView style={{ 
                        backgroundColor: "white", 
                        padding: 10, 
                        borderRadius: 5,
                        borderLeftWidth: 3,
                        borderLeftColor: "#28a745"
                     }}>
                        <ThemedText style={{ fontWeight: "bold" }}>
                           {connectedDevice.deviceName}
                        </ThemedText>
                        <ThemedText style={{ fontSize: 12, color: "#666" }}>
                           MAC: {connectedDevice.mac}
                        </ThemedText>
                        
                        <Pressable
                           style={{
                              backgroundColor: "#dc3545",
                              padding: 8,
                              borderRadius: 3,
                              marginTop: 8
                           }}
                           onPress={handleDisconnectDevice}
                        >
                           <ThemedText style={{ color: "white", textAlign: "center", fontSize: 12 }}>
                              Disconnect
                           </ThemedText>
                        </Pressable>
                     </ThemedView>
                  </ThemedView>
               )}

               {/* Activity Logs */}
               <ThemedView style={{ marginBottom: 15 }}>
                  <ThemedText style={{ fontWeight: "bold", marginBottom: 10 }}>
                     📋 Activity Log:
                  </ThemedText>
                  <ThemedView style={{ 
                     backgroundColor: "white", 
                     padding: 10, 
                     borderRadius: 5, 
                     maxHeight: 200 
                  }}>
                     <ScrollView style={{ maxHeight: 180 }}>
                        {logs.length === 0 ? (
                           <ThemedText style={{ color: "#666", fontStyle: "italic" }}>
                              No activity yet...
                           </ThemedText>
                        ) : (
                           logs.map((log, index) => (
                              <ThemedText key={index} style={{ fontSize: 12, marginBottom: 2 }}>
                                 {log}
                              </ThemedText>
                           ))
                        )}
                     </ScrollView>
                  </ThemedView>
               </ThemedView>

               {/* Instructions */}
               <ThemedView style={{ 
                  backgroundColor: "#e7f3ff", 
                  padding: 10, 
                  borderRadius: 5,
                  borderLeftWidth: 3,
                  borderLeftColor: "#007bff"
               }}>
                  <ThemedText style={{ fontSize: 12, fontWeight: "bold", marginBottom: 5 }}>
                     📝 Instructions:
                  </ThemedText>
                  <ThemedText style={{ fontSize: 12, lineHeight: 16 }}>
                     1. Tap "Start Scan" to search for your HS2S Pro scale{'\n'}
                     2. Step on your scale to power it on during scanning{'\n'}
                     3. When device appears, tap "Connect"{'\n'}
                     4. Once connected, use "Test Device" then step on scale for measurements
                  </ThemedText>
               </ThemedView>
            </ThemedView>

            {/* Quick Navigation */}
            <ThemedView style={{ gap: 15 }}>
               <Link href="/profile" asChild>
                  <Pressable style={{
                     backgroundColor: "#007bff",
                     padding: 15,
                     borderRadius: 8,
                     alignItems: "center"
                  }}>
                     <ThemedText style={{ color: "white", fontSize: 16, fontWeight: "bold" }}>
                        👤 Profile
                     </ThemedText>
                  </Pressable>
               </Link>

               <Link href="/devices" asChild>
                  <Pressable style={{
                     backgroundColor: "#28a745",
                     padding: 15,
                     borderRadius: 8,
                     alignItems: "center"
                  }}>
                     <ThemedText style={{ color: "white", fontSize: 16, fontWeight: "bold" }}>
                        📱 Devices
                     </ThemedText>
                  </Pressable>
               </Link>

               <Link href="/reading-list" asChild>
                  <Pressable style={{
                     backgroundColor: "#17a2b8",
                     padding: 15,
                     borderRadius: 8,
                     alignItems: "center"
                  }}>
                     <ThemedText style={{ color: "white", fontSize: 16, fontWeight: "bold" }}>
                        📊 Reading List
                     </ThemedText>
                  </Pressable>
               </Link>
            </ThemedView>
         </ScrollView>
      </ThemedView>
   );
}
