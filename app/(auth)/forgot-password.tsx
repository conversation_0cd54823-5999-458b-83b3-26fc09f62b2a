import React, { Fragment, useState } from "react";
import {
   View,
   Text,
   TextInput,
   SafeAreaView,
   KeyboardAvoidingView,
   Platform,
   Pressable,
   ActivityIndicator,
   ScrollView,
   Image,
} from "react-native";
import { useRouter } from "expo-router";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { getCommonStyles } from "@/constants/styles";
import { getTypography } from "@/constants/typography";
import { useTheme } from "@/context/ThemeContext";
import ToastMessage from "@/components/ui/ToastMessage";
import useApi from "@/hooks/useApi";

const ForgotPasswordScreen: React.FC = () => {
   const { theme, colors } = useTheme();
   const router = useRouter();
   const { post } = useApi();

   const [email, setEmail] = useState("");
   const [isResetSuccess, setIsResetSuccess] = useState(false);
   const [isLoading, setIsLoading] = useState(false);
   const [error, setError] = useState<string | null>(null);
   const [toast, setToast] = useState({ message: "", visible: false });

   const validateForm = () => {
      if (!email.trim()) {
         setError("Email is required");
         return false;
      } else if (!/\S+@\S+\.\S+/.test(email)) {
         setError("Email is invalid");
         return false;
      }

      setError(null);
      return true;
   };

   const handleSubmit = async () => {
      if (!validateForm()) {
         return;
      }

      setIsLoading(true);
      setError(null);

      const newToast = {
         message: "",
         visible: false,
      };

      try {
         await post<string>("/forgot-password", { data: { email } });

         console.log("A request to reset the password was sent successfully.");
         setIsResetSuccess(true);
      } catch (error) {
         newToast.visible = true;

         if (error instanceof Error) newToast.message = error.message;
         else newToast.message = "An unexpected error occurred.";

         setToast(newToast);
      } finally {
         setIsLoading(false);
      }
   };

   const handleReturn = () => {
      setIsResetSuccess(false);
      setError(null);
      router.navigate("/(auth)/login");
   };

   const styles = getCommonStyles(colors);
   const typography = getTypography(theme);

   return (
      <KeyboardAvoidingView
         behavior={Platform.OS === "ios" ? "padding" : "height"}
         style={styles.keyboardAvoidingView}
      >
         <ToastMessage
            message={toast.message || ""}
            visible={toast.visible}
            onHide={() => setToast({ message: "", visible: false })}
         />
         <ScrollView
            contentContainerStyle={styles.scrollViewContent}
            keyboardShouldPersistTaps="handled"
         >
            <View style={styles.formContainer}>
               <View style={styles.logoContainer}>
                  <Image
                     source={
                        theme === "light"
                           ? require("@/assets/images/adaptit_logo_dark.png")
                           : require("@/assets/images/adaptit_logo_light.png")
                     }
                     style={styles.logo}
                  />
               </View>

               <Text style={styles.title}>Reset your password</Text>

               {!isResetSuccess ? (
                  <Fragment>
                     <View
                        style={[
                           styles.inputGroup,
                           error && {
                              borderColor: colors.error,
                              borderWidth: 1.2,
                           },
                        ]}
                     >
                        <MaterialIcons
                           name="email"
                           size={20}
                           color={colors.icon}
                           style={styles.inputIcon}
                        />

                        <TextInput
                           style={styles.input}
                           placeholder="Email"
                           placeholderTextColor={colors.icon}
                           value={email}
                           onChangeText={setEmail}
                           keyboardType="email-address"
                           autoCapitalize="none"
                           autoComplete="email"
                           textContentType="emailAddress"
                        />
                     </View>
                     {error && <Text style={styles.errorText}>{error}</Text>}

                     <Pressable
                        style={({ pressed }) => [
                           styles.button,
                           pressed && styles.buttonPressed,
                           isLoading && styles.buttonDisabled,
                        ]}
                        onPress={handleSubmit}
                        disabled={isLoading}
                     >
                        {isLoading ? (
                           <ActivityIndicator color={colors.text} />
                        ) : (
                           <Text style={styles.buttonText}>Next</Text>
                        )}
                     </Pressable>
                  </Fragment>
               ) : (
                  <Fragment>
                     <Text style={typography.subtitle}>
                        A link to reset your password was sent to your email.
                     </Text>

                     <Pressable
                        style={({ pressed }) => [
                           styles.button,
                           pressed && styles.buttonPressed,
                           isLoading && styles.buttonDisabled,
                        ]}
                        onPress={handleReturn}
                        disabled={isLoading}
                     >
                        <Text style={styles.buttonText}>Back to login</Text>
                     </Pressable>
                  </Fragment>
               )}
            </View>
         </ScrollView>
      </KeyboardAvoidingView>
   );
};

export default ForgotPasswordScreen;
