import { Link, useRouter } from "expo-router";
import React, { useState } from "react";
import {
   View,
   Text,
   TextInput,
   KeyboardAvoidingView,
   Platform,
   Pressable,
   ActivityIndicator,
   ScrollView,
   Image,
} from "react-native";

type LoginFormErrors = {
   email?: string | null;
   password?: string | null;
};

import ToastMessage from "@/components/ui/ToastMessage";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { LoginResponse } from "@/types/auth";
import { useAuth } from "@/context/AuthContext";
import { getCommonStyles } from "@/constants/styles";
import { useTheme } from "@/context/ThemeContext";
import useApi from "@/hooks/useApi";

const LoginScreen: React.FC = () => {
   const { theme, colors } = useTheme();
   const { signIn } = useAuth();
   const router = useRouter();
   const styles = getCommonStyles(colors);
   const { post } = useApi();

   const [form, setForm] = useState({
      email: "",
      password: "",
   });
   const [errors, setErrors] = useState<LoginFormErrors>({});
   const [apiError, setApiError] = useState("");
   const [isLoading, setIsLoading] = useState(false);
   const [showPassword, setShowPassword] = useState(false);

   const validateLoginForm = (): boolean => {
      setErrors({ email: null, password: null });
      const newErrors: LoginFormErrors = {};

      const email = form.email.trim();
      const password = form.password.trim();

      if (!email) {
         newErrors.email = "Email is required";
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/.test(email))
         newErrors.email = "Email is invalid";

      if (!password) {
         newErrors.password = "Password is required";
      }

      setErrors(newErrors);
      return !newErrors.email && !newErrors.password;
   };

   const handleLogin = async () => {
      if (!validateLoginForm()) return;

      setIsLoading(true);
      setApiError("");

      try {
         const response = await post<
            LoginResponse,
            { email: string; password: string }
         >("/users/login", {
            data: {
               email: form.email,
               password: form.password,
            },
            retry: false,
         });

         console.log("Login successful:", response);
         setForm({ email: "", password: "" });

         await signIn(response);
         router.push("/(app)");
      } catch (error) {
         let message = "Login failed. An unexpected error occurred.";

         if (error instanceof Error) {
            if (error.message?.toLowerCase().includes("invalid"))
               message = "Incorrect email or password.";
            else if (error.message?.toLowerCase().includes("at least"))
               message = "Password must have at least 8 characters";
         }

         setApiError(message);
      } finally {
         setIsLoading(false);
      }
   };

   return (
      <KeyboardAvoidingView
         behavior={Platform.OS === "ios" ? "padding" : "height"}
         style={styles.keyboardAvoidingView}
      >
         <ToastMessage
            visible={apiError.length > 0}
            message={apiError}
            // onHide={() => setApiError("")}
         />
         <ScrollView
            contentContainerStyle={styles.scrollViewContent}
            keyboardShouldPersistTaps="handled"
         >
            <View style={styles.formContainer}>
               <View style={styles.logoContainer}>
                  <Image
                     source={
                        theme === "light"
                           ? require("@/assets/images/adaptit_logo_dark.png")
                           : require("@/assets/images/adaptit_logo_light.png")
                     }
                     style={styles.logo}
                  />
               </View>
               <Text style={styles.title}>Welcome back</Text>

               <View
                  style={[
                     styles.inputGroup,
                     errors.email && {
                        borderColor: colors.error,
                        borderWidth: 1.2,
                     },
                  ]}
               >
                  <MaterialIcons
                     name="email"
                     size={20}
                     color={colors.icon}
                     style={styles.inputIcon}
                  />
                  <TextInput
                     style={styles.input}
                     placeholder="Email"
                     placeholderTextColor={colors.icon}
                     value={form.email}
                     onChangeText={(email) => setForm({ ...form, email })}
                     keyboardType="email-address"
                     autoCapitalize="none"
                     autoComplete="email"
                     textContentType="emailAddress"
                  />
               </View>

               {errors.email && (
                  <Text style={styles.errorText}>{errors.email}</Text>
               )}

               <View
                  style={[
                     styles.inputGroup,
                     errors.password && {
                        borderColor: colors.error,
                        borderWidth: 1.2,
                     },
                  ]}
               >
                  <MaterialIcons
                     name="lock"
                     size={20}
                     color={colors.icon}
                     style={styles.inputIcon}
                  />
                  <TextInput
                     style={styles.input}
                     placeholder="Password"
                     placeholderTextColor={colors.icon}
                     value={form.password}
                     onChangeText={(password) => setForm({ ...form, password })}
                     secureTextEntry={!showPassword}
                     autoComplete="current-password"
                     textContentType="password"
                  />
                  <Pressable
                     onPress={() => setShowPassword(!showPassword)}
                     style={styles.eyeButton}
                  >
                     <MaterialIcons
                        name={showPassword ? "visibility-off" : "visibility"}
                        size={20}
                        color={colors.icon}
                     />
                  </Pressable>
               </View>
               {errors.password && (
                  <Text style={styles.errorText}>{errors.password}</Text>
               )}

               <Link href="/(auth)/forgot-password" asChild>
                  <Pressable style={styles.forgotPasswordContainer}>
                     <Text style={styles.forgotPasswordText}>
                        Forgot Password?
                     </Text>
                  </Pressable>
               </Link>

               <Pressable
                  style={({ pressed }) => [
                     styles.button,
                     pressed && styles.buttonPressed,
                     isLoading && styles.buttonDisabled,
                  ]}
                  onPress={handleLogin}
                  disabled={isLoading}
               >
                  {isLoading ? (
                     <ActivityIndicator color={colors.text} />
                  ) : (
                     <Text style={styles.buttonText}>Login</Text>
                  )}
               </Pressable>

               <View style={styles.footer}>
                  <Text style={styles.footerText}>
                     Do not have an account?{" "}
                  </Text>
                  <Link href="/(auth)/signup" replace asChild>
                     <Pressable>
                        <Text style={styles.linkText}>Sign Up</Text>
                     </Pressable>
                  </Link>
               </View>
            </View>
         </ScrollView>
      </KeyboardAvoidingView>
   );
};

export default LoginScreen;
