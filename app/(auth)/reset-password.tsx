import { useLocalSearchParams, useRouter } from "expo-router";
import React, { Fragment, useState } from "react";
import {
   View,
   Text,
   TextInput,
   SafeAreaView,
   KeyboardAvoidingView,
   Platform,
   Pressable,
   ActivityIndicator,
   ScrollView,
   Image,
   Alert,
} from "react-native";

import { Colors } from "@/constants/colors";
import { getCommonStyles } from "@/constants/styles";
import { useTheme } from "@/context/ThemeContext";
import useApi from "@/hooks/useApi";

const ResetPasswordScreen: React.FC = () => {
   const { token } = useLocalSearchParams<{ token?: string }>();
   const { theme, colors } = useTheme();
   const styles = getCommonStyles(colors);
   const router = useRouter();
   const { post } = useApi();

   const [newPassword, setNewPassword] = useState("");
   const [isLoading, setIsLoading] = useState(false);
   const [isPasswordReset, setIsPasswordReset] = useState(false);

   const [newPasswordError, setNewPasswordError] = useState<string | null>(
      null,
   );
   const [apiError, setApiError] = useState<string | null>(null);

   const validateForm = () => {
      let isValid = true;
      setApiError(null);

      if (!newPassword.trim()) {
         setNewPasswordError("Enter a new password");
         isValid = false;
      }

      return isValid;
   };

   const handleSubmit = async () => {
      if (!validateForm()) {
         return;
      }

      setIsLoading(true);
      setApiError(null);

      try {
         console.log(token);
         const response = await post<string>("/reset-password", {
            data: { newPassword },
            token,
         });

         console.log(response);
         setIsPasswordReset(true);
      } catch (error) {
         if (error instanceof Error) {
            setApiError(error.message);
            Alert.alert("Server Error", error.message);
         } else {
            setApiError("An unexpected error occurred.");
            Alert.alert("Server Error", "An unexpected error occurred.");
         }
      } finally {
         setIsLoading(false);
      }
   };

   return (
      <KeyboardAvoidingView
         behavior={Platform.OS === "ios" ? "padding" : "height"}
         style={styles.keyboardAvoidingView}
      >
         <ScrollView
            contentContainerStyle={styles.scrollViewContent}
            keyboardShouldPersistTaps="handled"
         >
            <View style={styles.formContainer}>
               <View style={styles.logoContainer}>
                  <Image
                     source={
                        theme === "light"
                           ? require("@/assets/images/adaptit_logo_dark.png")
                           : require("@/assets/images/adaptit_logo_light.png")
                     }
                     style={styles.logo}
                  />
               </View>

               {!isPasswordReset ? (
                  <Fragment>
                     {/* --- Header Text --- */}
                     <Text style={styles.title}>Reset your password</Text>
                     {apiError && (
                        <Text style={[styles.errorText, styles.apiErrorText]}>
                           {apiError}
                        </Text>
                     )}

                     <View style={styles.inputGroup}>
                        <TextInput
                           style={styles.input}
                           placeholder="New password"
                           placeholderTextColor={colors.icon}
                           value={newPassword}
                           onChangeText={setNewPassword}
                           keyboardType="default"
                           secureTextEntry
                           autoComplete="password-new"
                           textContentType="newPassword"
                        />
                     </View>
                     {newPasswordError && (
                        <Text style={styles.errorText}>{newPasswordError}</Text>
                     )}

                     <Pressable
                        style={({ pressed }) => [
                           styles.button,
                           pressed && styles.buttonPressed,
                           isLoading && styles.buttonDisabled,
                        ]}
                        onPress={handleSubmit}
                        disabled={isLoading}
                     >
                        {isLoading ? (
                           <ActivityIndicator color={Colors.dark.text} />
                        ) : (
                           <Text style={styles.buttonText}>Reset</Text>
                        )}
                     </Pressable>
                  </Fragment>
               ) : (
                  <Fragment>
                     <Text style={styles.title}>Password Changed</Text>
                     <Text style={styles.subtitle}>
                        Your password has been reset.
                     </Text>

                     <Pressable
                        style={({ pressed }) => [
                           styles.button,
                           pressed && styles.buttonPressed,
                        ]}
                        disabled={isLoading}
                        onPress={() => router.navigate("/(auth)/login")}
                     >
                        <Text style={styles.buttonText}>Back to login</Text>
                     </Pressable>
                  </Fragment>
               )}
            </View>
         </ScrollView>
      </KeyboardAvoidingView>
   );
};

export default ResetPasswordScreen;
