import React from "react";
import { AuthProvider } from "@/context/AuthContext";
import { ThemeProvider } from "@/context/ThemeContext";
import { FormProvider } from "@/context/FormContext";
import AuthenticatedLayout from "./_authenticated-layout";

const RootLayout: React.FC = () => (
   <AuthProvider>
      <ThemeProvider>
         <FormProvider>
            <AuthenticatedLayout />
         </FormProvider>
      </ThemeProvider>
   </AuthProvider>
);

export default RootLayout;
