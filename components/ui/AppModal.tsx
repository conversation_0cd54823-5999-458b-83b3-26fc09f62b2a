import React from "react";
import { Modal, TouchableOpacity, View, Text, StyleSheet } from "react-native";
import { Colors } from "@/constants/colors";

const AppModal = ({
   visible,
   onRequestClose,
   children,
   title,
   colorScheme = "light",
}: {
   visible: boolean;
   onRequestClose: () => void;
   children: React.ReactNode;
   title?: string;
   colorScheme?: "light" | "dark";
}) => {
   return (
      <Modal
         visible={visible}
         transparent
         animationType="slide"
         onRequestClose={onRequestClose}
      >
         <TouchableOpacity
            style={modalStyles.overlay}
            activeOpacity={1}
            onPressOut={onRequestClose}
         >
            <View
               style={[
                  modalStyles.modalContainer,
                  { backgroundColor: Colors[colorScheme].background },
               ]}
            >
               {title ? (
                  <Text
                     style={[
                        modalStyles.modalTitle,
                        { color: Colors[colorScheme].text },
                     ]}
                  >
                     {title}
                  </Text>
               ) : null}
               {children}
            </View>
         </TouchableOpacity>
      </Modal>
   );
};

const modalStyles = StyleSheet.create({
   overlay: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.5)",
      justifyContent: "center",
      alignItems: "center",
   },
   modalContainer: {
      borderRadius: 12,
      padding: 20,
      width: "85%",
      maxWidth: 300,
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
   },
   modalTitle: {
      fontSize: 18,
      fontWeight: "600",
      marginBottom: 15,
   },
});

export default AppModal;
