import { View, Text, StyleSheet } from "react-native";
import { Image } from "expo-image";

import { Colors } from "@/constants/colors";
import { spacing } from "@/constants/spacing";
import { ThemeColors, useTheme } from "@/context/ThemeContext";

interface Device {
   id: string;
   name: string;
   brand: string;
   type: string;
   readings: number;
   imageUrl: string;
   backgroundColor: string;
}

const DeviceCard: React.FC<{
   device: Device;
}> = ({ device }) => {
   const { colors } = useTheme();
   const styles = getStyles(colors);

   return (
      <View style={styles.deviceCard}>
         <View style={styles.deviceIcon}>
            <Image style={styles.deviceImage} source={device.imageUrl} />
         </View>
         <View style={styles.deviceInfo}>
            <Text style={styles.deviceName}>{device.name}</Text>
            <Text style={styles.deviceBrand}>By {device.brand}</Text>
            <Text style={styles.deviceType}>{device.type}</Text>
         </View>
         <View style={styles.readingsContainer}>
            <Text style={styles.readingsCount}>0 readings</Text>
         </View>
      </View>
   );
};

const getStyles = (colors: ThemeColors) =>
   StyleSheet.create({
      deviceImage: {
         flex: 1,
         width: "100%",
      },
      container: {
         flex: 1,
         backgroundColor: colors.background,
         paddingHorizontal: spacing.lg,
         paddingTop: spacing.md,
      },
      deviceCard: {
         flexDirection: "row",
         alignItems: "center",
         backgroundColor: colors.inputBackground,
         borderRadius: 16,
         padding: spacing.md,
         shadowColor: "#000",
         shadowOffset: {
            width: 0,
            height: 2,
         },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 4,
      },
      deviceIcon: {
         width: 60,
         height: 60,
         borderRadius: 12,
         justifyContent: "center",
         alignItems: "center",
         marginRight: spacing.md,
      },
      deviceInfo: {
         flex: 1,
      },
      deviceName: {
         fontSize: 18,
         fontWeight: "bold",
         color: colors.text,
         marginBottom: 2,
      },
      deviceBrand: {
         fontSize: 14,
         color: Colors.light.tint,
         marginBottom: 2,
      },
      deviceType: {
         fontSize: 14,
         color: colors.icon,
      },
      readingsContainer: {
         alignItems: "flex-end",
      },
      readingsCount: {
         fontSize: 14,
         color: colors.text,
         fontWeight: "500",
      },
   });

export default DeviceCard;
