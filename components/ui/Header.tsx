import React from "react";
import { View, Image } from "react-native";

import { getCommonStyles } from "@/constants/styles";
import { useTheme } from "@/context/ThemeContext";

const Header: React.FC = () => {
   const { theme, colors } = useTheme();
   const styles = getCommonStyles(colors);

   return (
      <View style={styles.header}>
         <Image
            source={
               theme === "light"
                  ? require("@/assets/images/adaptit_logo_dark.png")
                  : require("@/assets/images/adaptit_logo_light.png")
            }
            style={styles.logo}
            resizeMode="contain"
         />
      </View>
   );
};

export default Header;
