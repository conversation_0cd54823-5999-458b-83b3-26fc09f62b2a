import React from "react";
import { StyleSheet, useColorScheme, View } from "react-native";

const HorizontalRule: React.FC<{
   marginTop?: number;
   marginBottom?: number;
}> = ({ marginTop = 0, marginBottom = 0 }) => {
   const colorScheme = useColorScheme() ?? "light";

   return (
      <View
         style={{
            marginBottom,
            marginTop,
            borderBottomColor: colorScheme === "light" ? "gray" : "white",
            borderBottomWidth: StyleSheet.hairlineWidth,
         }}
      />
   );
};

export default HorizontalRule;
