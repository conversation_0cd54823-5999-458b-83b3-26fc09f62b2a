import React, { useMemo, useCallback } from "react";
import { View } from "react-native";
import { useRouter, useSegments } from "expo-router";

import { getCommonStyles } from "@/constants/styles";
import { useTheme } from "@/context/ThemeContext";
import TabItem, { Tab } from "./TabItem";

const Navigation: React.FC<{ tabs: Tab[] }> = ({ tabs }) => {
   const { colors } = useTheme();
   const router = useRouter();
   const segments = useSegments();

   const currentRoute = useMemo(
      () => `/${segments[1] ?? ""}`.toLowerCase(),
      [segments],
   );

   const styles = useMemo(() => getCommonStyles(colors), [colors]);

   const handleTabPress = useCallback(
      (route: string) => {
         router.push(route as any);
      },
      [router],
   );

   return (
      <View
         style={[
            styles.tabBar,
            {
               backgroundColor: colors.background,
               borderTopColor: "transparent",
            },
         ]}
      >
         {tabs.map((tab) => {
            const focused = currentRoute === tab.route;

            return (
               <TabItem
                  key={tab.label}
                  tab={tab}
                  focused={focused}
                  onPress={() => handleTabPress(tab.route)}
               />
            );
         })}
      </View>
   );
};

export default Navigation;
