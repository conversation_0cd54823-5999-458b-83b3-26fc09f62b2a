import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { Ionicons } from "@expo/vector-icons";

import { useTheme } from "@/context/ThemeContext";
import { getCommonStyles } from "@/constants/styles";
import { spacing } from "@/constants/spacing";

export interface Tab {
   label: string;
   icon: string;
   iconFocused: string;
   route: string;
}

const TabItem: React.FC<{
   tab: Tab;
   focused: boolean;
   onPress: () => void;
}> = ({ tab, focused, onPress }) => {
   const { colors } = useTheme();
   const styles = getCommonStyles(colors);

   const iconName = focused ? tab.iconFocused : tab.icon;
   const iconColor = focused ? colors.tabIconSelected : colors.tabIconDefault;
   const iconElement = (
      <Ionicons name={iconName as any} size={22} color={iconColor} />
   );

   return (
      <TouchableOpacity
         key={tab.label}
         style={styles.tab}
         activeOpacity={0.7}
         onPress={onPress}
      >
         <View style={styles.iconLabelWrapper}>
            {iconElement}
            <Text
               style={[
                  focused && { fontWeight: "bold" },
                  !focused && { fontWeight: "normal" },
                  { marginTop: spacing.xs, color: colors.text },
               ]}
            >
               {tab.label}
            </Text>
         </View>
      </TouchableOpacity>
   );
};

export default TabItem;
