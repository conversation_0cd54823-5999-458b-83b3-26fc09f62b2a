import React, {
   createContext,
   useContext,
   useState,
   useEffect,
   ReactNode,
} from "react";

import { storage } from "@/utils/storage";
import { LoginResponse, BackendUser } from "@/types/auth";
import { request } from "@/service/api";

interface AuthState {
   accessToken: string | null;
   refreshToken: string | null;
   user: BackendUser | null;
   isAuthenticated: boolean;
   isLoading: boolean;
   isSessionExpiring: boolean;
}

interface AuthContextType extends AuthState {
   signIn: (data: LoginResponse) => Promise<void>;
   processSignupSuccess: (userData: BackendUser) => void;
   signOut: () => Promise<void>;
   getAccessToken: () => Promise<string | null>;
   updateUser: (user: BackendUser) => void;
   acknowledgeSessionExpiry: () => void;
}

const ACCESS_TOKEN_KEY = "accessToken";
const REFRESH_TOKEN_KEY = "refreshToken";
const USER_INFO_KEY = "userInfo";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
   children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
   const [authState, setAuthState] = useState<AuthState>({
      accessToken: null,
      refreshToken: null,
      user: null,
      isAuthenticated: false,
      isLoading: true,
      isSessionExpiring: false,
   });

   useEffect(() => {
      const loadAuthData = async () => {
         try {
            const accessToken = await storage.getItem(ACCESS_TOKEN_KEY);
            const refreshToken = await storage.getItem(REFRESH_TOKEN_KEY);
            const userInfoStr = await storage.getItem(USER_INFO_KEY);

            let user: BackendUser | null = null;

            if (userInfoStr) {
               try {
                  user = JSON.parse(userInfoStr);
               } catch (e) {
                  console.error("Failed to parse user info from storage", e);
               }
            }

            if (accessToken && refreshToken) {
               setAuthState({
                  ...authState,
                  accessToken,
                  refreshToken,
                  user,
                  isAuthenticated: true,
                  isLoading: false,
               });
               console.log("Auth tokens loaded from storage.");
            } else {
               setAuthState((prev) => ({
                  ...prev,
                  user: null,
                  isLoading: false,
               }));
            }
         } catch (e) {
            console.error("Failed to load auth data from storage", e);
            setAuthState((prev) => ({ ...prev, user: null, isLoading: false }));
         }
      };

      loadAuthData();
   }, []);

   const signIn = async (data: LoginResponse) => {
      try {
         await storage.setItem(ACCESS_TOKEN_KEY, String(data.access_token));
         await storage.setItem(REFRESH_TOKEN_KEY, String(data.refresh_token));

         // useApi hook cannot be used here because it is dependent on this context
         const user: BackendUser = await request<BackendUser>(
            "GET",
            "/users/me",
            true,
            undefined,
            data.access_token,
         );

         await storage.setItem(USER_INFO_KEY, JSON.stringify(user));

         setAuthState({
            accessToken: data.access_token,
            refreshToken: data.refresh_token,
            user: user,
            isAuthenticated: true,
            isLoading: false,
            isSessionExpiring: false,
         });

         console.log("Access, refresh tokens, and user info saved to storage.");
      } catch (e) {
         console.error("Failed to fetch or store user info", e);
      }
   };

   // TODO: What is the point of this function??
   const processSignupSuccess = async (userData: BackendUser) => {
      try {
         console.log("User registered, basic info stored:", userData);
      } catch (e) {
         console.error("Failed to process signup data", e);
      }
   };

   const signOut = async () => {
      const currentRefreshToken = authState.refreshToken;

      setAuthState({
         accessToken: null,
         refreshToken: null,
         user: null,
         isAuthenticated: true,
         isLoading: false,
         isSessionExpiring: true,
      });

      try {
         await storage.removeItem(ACCESS_TOKEN_KEY);
         await storage.removeItem(REFRESH_TOKEN_KEY);
         await storage.removeItem(USER_INFO_KEY);

         console.log("Auth tokens deleted from storage.");

         if (currentRefreshToken) {
            try {
               await request<string>(
                  "POST",
                  "/users/logout",
                  false,
                  {},
                  currentRefreshToken,
               );
               console.log("Successfully logged out from backend.");
            } catch (e) {
               console.warn(
                  "Failed to logout from backend, but cleared local session.",
                  e,
               );
            }
         }
      } catch (e) {
         console.error("Failed to delete auth data", e);
      }
   };

   const getAccessToken = async (): Promise<string | null> => {
      if (authState.accessToken) {
         return authState.accessToken;
      }
      return null;
   };

   const updateUser = (user: BackendUser) => {
      setAuthState((prev) => ({
         ...prev,
         user,
      }));
   };

   const acknowledgeSessionExpiry = (): void => {
      setAuthState((prev) => ({
         ...prev,
         isAuthenticated: false,
         isSessionExpiring: false,
      }));
   };

   const value = {
      ...authState,
      signIn,
      processSignupSuccess,
      signOut,
      getAccessToken,
      updateUser,
      acknowledgeSessionExpiry,
   };

   return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
   const context = useContext(AuthContext);
   if (context === undefined) {
      throw new Error("useAuth must be used within an AuthProvider");
   }
   return context;
};
