import React, { createContext, ReactNode, useContext } from "react";
import { useColorScheme } from "react-native";

import { Colors } from "@/constants/colors";

export type ThemeColors = (typeof Colors)[keyof typeof Colors];

interface ThemeContextType {
   theme: "light" | "dark";
   colors: ThemeColors;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({
   children,
}) => {
   const colorScheme = useColorScheme() || "light";
   const value = { theme: colorScheme, colors: Colors[colorScheme] };

   console.log(`Using color scheme: ${colorScheme}`);

   return (
      <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
   );
};

export const useTheme = () => {
   const context = useContext(ThemeContext);
   if (context === undefined)
      throw new Error("useTheme must be used within a ThemeProvider");

   return context;
};
