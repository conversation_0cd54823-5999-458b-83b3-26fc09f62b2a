import { useAuth } from "@/context/AuthContext";
import { request, HTTPMethod } from "@/service/api";
import { SessionExpiredError } from "@/utils/SessionExpiredError";

const useApi = () => {
   const { signOut } = useAuth();

   const _createRequest = async <TResponse>(
      method: HTTPMethod,
      endpoint: string,
      data: any,
      token?: string,
      retry: boolean = true,
   ): Promise<TResponse> => {
      try {
         return await request(method, endpoint, retry, data, token);
      } catch (error) {
         console.warn(error);

         // Sign out the user if all their tokens have expired
         if (error instanceof SessionExpiredError) {
            console.warn("Session has expired. Signing out...");
            await signOut();
            return {} as TResponse;
         }

         throw new Error(
            error instanceof Error
               ? error.message
               : "An unknown network error occurred.",
         );
      }
   };

   return {
      post: async <TResponse, TRequest = Record<string, any>>(
         endpoint: string,
         options?: {
            data?: TRequest;
            token?: string;
            retry?: boolean;
         },
      ): Promise<TResponse> => {
         const { token, data, retry = true } = options || {};
         return _createRequest("POST", endpoint, data, token, retry);
      },

      patch: async <TResponse, TRequest = Record<string, any>>(
         endpoint: string,
         options?: {
            data?: TRequest;
            token?: string;
            retry?: boolean;
         },
      ): Promise<TResponse> => {
         const { token, data, retry = true } = options || {};
         return _createRequest("PATCH", endpoint, data, token, retry);
      },

      put: async <TResponse, TRequest = Record<string, any>>(
         endpoint: string,
         options?: {
            data?: TRequest;
            token?: string;
            retry?: boolean;
         },
      ): Promise<TResponse> => {
         const { token, data, retry = true } = options || {};
         return _createRequest("PUT", endpoint, data, token, retry);
      },

      get: async <TResponse>(
         endpoint: string,
         options?: {
            token?: string;
            retry?: boolean;
         },
      ): Promise<TResponse> => {
         const { token, retry = true } = options || {};
         return _createRequest("GET", endpoint, undefined, token, retry);
      },

      delete_: async <TResponse>(
         endpoint: string,
         options?: {
            token?: string;
            retry?: boolean;
         },
      ): Promise<TResponse> => {
         const { token, retry = true } = options || {};
         return _createRequest("DELETE", endpoint, undefined, token, retry);
      },
   };
};

export default useApi;
