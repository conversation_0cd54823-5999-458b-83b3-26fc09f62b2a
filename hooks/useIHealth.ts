import { useState, useEffect, useCallback } from "react";
import { Platform, PermissionsAndroid } from "react-native";
import iHealthService, {
   BodyScaleReading,
   DeviceInfo,
   DeviceConnectionStatus,
} from "@/service/iHealthService";

export interface UseIHealthReturn {
   // State
   connectionStatus: DeviceConnectionStatus;
   connectedDevice: DeviceInfo | null;
   discoveredDevices: DeviceInfo[];
   lastReading: BodyScaleReading | null;
   isScanning: boolean;
   error: string | null;
   isSDKAvailable: boolean;

   // Actions
   startScan: () => Promise<void>;
   stopScan: () => Promise<void>;
   connectDevice: (mac: string) => Promise<boolean>;
   disconnectDevice: () => Promise<void>;
   requestPermissions: () => Promise<boolean>;
   clearError: () => void;
}

export const useIHealth = (): UseIHealthReturn => {
   const [connectionStatus, setConnectionStatus] =
      useState<DeviceConnectionStatus>("disconnected");
   const [connectedDevice, setConnectedDevice] = useState<DeviceInfo | null>(
      null,
   );
   const [discoveredDevices, setDiscoveredDevices] = useState<DeviceInfo[]>([]);
   const [lastReading, setLastReading] = useState<BodyScaleReading | null>(
      null,
   );
   const [isScanning, setIsScanning] = useState(false);
   const [error, setError] = useState<string | null>(null);
   const [isSDKAvailable, setIsSDKAvailable] = useState(false);

   // Check SDK availability and setup listeners
   useEffect(() => {
      // Check if SDK is available
      setIsSDKAvailable(iHealthService.isSDKAvailable());

      if (!iHealthService.isSDKAvailable()) {
         setError("iHealth SDK not available - module not found");
         return;
      }

      // Setup event listeners
      const handleDeviceDiscovered = (device: DeviceInfo) => {
         setDiscoveredDevices((prev) => {
            // Avoid duplicates
            const exists = prev.some((d) => d.mac === device.mac);
            if (exists) return prev;
            return [...prev, device];
         });
      };

      const handleDeviceConnected = (device: DeviceInfo) => {
         setConnectionStatus("connected");
         setConnectedDevice(device);
         setError(null);
      };

      const handleDeviceDisconnected = (device: DeviceInfo) => {
         setConnectionStatus("disconnected");
         setConnectedDevice(null);
      };

      const handleMeasurementReceived = (reading: BodyScaleReading) => {
         setLastReading(reading);
         console.log("New body scale reading received:", reading);
      };

      const handleDeviceError = (errorInfo: any) => {
         setError("Device error: " + JSON.stringify(errorInfo));
         setConnectionStatus("error");
      };

      // Add event listeners
      iHealthService.addEventListener(
         "deviceDiscovered",
         handleDeviceDiscovered,
      );
      iHealthService.addEventListener("deviceConnected", handleDeviceConnected);
      iHealthService.addEventListener(
         "deviceDisconnected",
         handleDeviceDisconnected,
      );
      iHealthService.addEventListener(
         "measurementReceived",
         handleMeasurementReceived,
      );
      iHealthService.addEventListener("deviceError", handleDeviceError);

      // Cleanup on unmount
      return () => {
         iHealthService.removeEventListener(
            "deviceDiscovered",
            handleDeviceDiscovered,
         );
         iHealthService.removeEventListener(
            "deviceConnected",
            handleDeviceConnected,
         );
         iHealthService.removeEventListener(
            "deviceDisconnected",
            handleDeviceDisconnected,
         );
         iHealthService.removeEventListener(
            "measurementReceived",
            handleMeasurementReceived,
         );
         iHealthService.removeEventListener("deviceError", handleDeviceError);
         iHealthService.cleanup();
      };
   }, []);

   // Request permissions (Android)
   const requestPermissions = useCallback(async (): Promise<boolean> => {
      if (Platform.OS !== "android") {
         return true; // iOS handles permissions automatically
      }

      try {
         const permissions = [
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            // Note: Bluetooth permissions handled by the iHealth SDK
         ];

         const granted = await PermissionsAndroid.requestMultiple(permissions);

         const allGranted = Object.values(granted).every(
            (permission) => permission === PermissionsAndroid.RESULTS.GRANTED,
         );

         if (!allGranted) {
            setError(
               "Location permissions are required for iHealth device discovery",
            );
            return false;
         }

         return true;
      } catch (err) {
         setError("Failed to request permissions: " + (err as Error).message);
         return false;
      }
   }, []);

   // Start scanning for devices
   const startScan = useCallback(async (): Promise<void> => {
      try {
         setError(null);
         setDiscoveredDevices([]);

         if (!isSDKAvailable) {
            setError("iHealth SDK not available");
            return;
         }

         // Request permissions first
         const hasPermissions = await requestPermissions();
         if (!hasPermissions) {
            return;
         }

         setIsScanning(true);
         await iHealthService.startScan();

         // Auto-stop scanning after 30 seconds
         setTimeout(() => {
            setIsScanning(false);
         }, 30000);
      } catch (err) {
         setError("Failed to start scan: " + (err as Error).message);
         setIsScanning(false);
      }
   }, [requestPermissions, isSDKAvailable]);

   // Stop scanning
   const stopScan = useCallback(async (): Promise<void> => {
      try {
         await iHealthService.stopScan();
         setIsScanning(false);
      } catch (err) {
         setError("Failed to stop scan: " + (err as Error).message);
      }
   }, []);

   // Connect to a device
   const connectDevice = useCallback(async (mac: string): Promise<boolean> => {
      try {
         setError(null);
         setConnectionStatus("connecting");

         const success = await iHealthService.connectDevice(mac);

         if (!success) {
            setError("Failed to connect to device");
            setConnectionStatus("error");
         }

         return success;
      } catch (err) {
         setError("Connection error: " + (err as Error).message);
         setConnectionStatus("error");
         return false;
      }
   }, []);

   // Disconnect from current device
   const disconnectDevice = useCallback(async (): Promise<void> => {
      try {
         await iHealthService.disconnectDevice();
         setConnectionStatus("disconnected");
         setConnectedDevice(null);
      } catch (err) {
         setError("Failed to disconnect: " + (err as Error).message);
      }
   }, []);

   // Clear error
   const clearError = useCallback(() => {
      setError(null);
   }, []);

   return {
      // State
      connectionStatus,
      connectedDevice,
      discoveredDevices,
      lastReading,
      isScanning,
      error,
      isSDKAvailable,

      // Actions
      startScan,
      stopScan,
      connectDevice,
      disconnectDevice,
      requestPermissions,
      clearError,
   };
};
