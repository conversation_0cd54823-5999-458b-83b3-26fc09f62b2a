import { useState } from "react";

type Strength = "weak" | "medium" | "strong" | "";

export const passwordRequirements = [
   {
      label: "At least 8 characters",
      test: (password: string) => password.length >= 8,
   },
   {
      label: "At least one uppercase letter",
      test: (password: string) => /[A-Z]/.test(password),
   },
   {
      label: "At least one number",
      test: (password: string) => /\d/.test(password),
   },
   {
      label: "At least one special character (!@#$%^&*)",
      test: (password: string) => /[!@#$%^&*]/.test(password),
   },
];

const calculatePasswordStrength = (password: string): Strength => {
   if (
      password.length >= 8 &&
      /[A-Z]/.test(password) &&
      /\d/.test(password) &&
      /[!@#$%^&*]/.test(password)
   ) {
      return "strong";
   } else if (
      password.length >= 6 &&
      /\d/.test(password) &&
      /[A-Za-z]/.test(password)
   ) {
      return "medium";
   } else if (password.length > 0) {
      return "weak";
   }
   return "";
};

export const strengthColors = {
   weak: "#ff4d4f",
   medium: "#f0ad4e",
   strong: "#5cb85c",
} as const;

export const strengthWidths = {
   weak: "33%",
   medium: "66%",
   strong: "100%",
} as const;

export const usePasswordStrength = () => {
   const [password, setPassword] = useState("");
   const [passwordStrength, setPasswordStrength] = useState<Strength>("");
   const [isPasswordFocused, setIsPasswordFocused] = useState(false);
   const [showPassword, setShowPassword] = useState(false);

   const handlePasswordChange = (value: string) => {
      setPassword(value);
      setPasswordStrength(calculatePasswordStrength(value));
   };

   return {
      password,
      setPassword,
      passwordStrength,
      isPasswordFocused,
      setIsPasswordFocused,
      showPassword,
      setShowPassword,
      handlePasswordChange,
      strengthColors,
      strengthWidths,
      passwordRequirements,
   };
};