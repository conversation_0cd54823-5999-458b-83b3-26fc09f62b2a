import { NativeModules, DeviceEventEmitter, Platform } from "react-native";

// Import iHealth modules
import { iHealthDeviceManagerModule } from "@ihealth/ihealthlibrary-react-native";

// Type definitions for iHealth PO3M Pulse Oximeter
export interface PulseOximeterReading {
   spo2: number; // Blood oxygen saturation percentage
   pulseRate: number; // Heart rate in BPM
   pi?: number; // Perfusion Index
   timestamp: string;
}

export interface DeviceInfo {
   mac: string;
   deviceType: string;
   deviceName: string;
   protocolString: string;
}

export type DeviceConnectionStatus =
   | "disconnected"
   | "connecting"
   | "connected"
   | "error";

class IHealthService {
   private connectionStatus: DeviceConnectionStatus = "disconnected";
   private connectedDevice: DeviceInfo | null = null;
   private discoveryTimeout: ReturnType<typeof setTimeout> | null = null;

   // Event listeners
   private listeners: { [key: string]: any[] } = {};
   private deviceEventListeners: any[] = [];

   constructor() {
      // Only setup listeners if module is available
      if (this.isSDKAvailable()) {
         this.authenticateSDK();
         this.setupEventListeners();
      }
   }

   /**
    * Authenticate with iHealth SDK using license file
    */
   private authenticateSDK() {
      try {
         console.log("🔐 Authenticating iHealth SDK...");
         // const filename = "com_anonymous_fhirmobilemvp_android.pem";
         const filename = "license.pem";
         iHealthDeviceManagerModule.sdkAuthWithLicense(filename);
         console.log("✅ SDK authentication completed");
      } catch (error) {
         console.error("❌ SDK authentication failed:", error);
      }
   }

   /**
    * Check if the current platform is supported
    */
   isPlatformSupported(): boolean {
      return Platform.OS === "android" || Platform.OS === "ios";
   }

   /**
    * Check if iHealth SDK is available
    */
   isSDKAvailable(): boolean {
      return !!iHealthDeviceManagerModule;
   }

   /**
    * Setup event listeners - enhanced version
    */
   private setupEventListeners() {
      if (!iHealthDeviceManagerModule) {
         return;
      }

      try {
         console.log("Setting up iHealth event listeners...");

         // CRITICAL FIX: Discovery event with multiple listener approaches
         console.log("🎧 Setting up Event_Scan_Device listeners...");
         console.log(
            "🎧 Event_Scan_Device value:",
            iHealthDeviceManagerModule.Event_Scan_Device,
         );

         // Try multiple event name variations
         const eventNames = [
            iHealthDeviceManagerModule.Event_Scan_Device,
            "Event_Scan_Device",
            "event_scan_device",
            "iHealthDeviceManagerModule.Event_Scan_Device",
            "scan_device",
         ].filter(Boolean);

         console.log("🎧 Trying event names:", eventNames);

         let listenerCount = 0;
         eventNames.forEach((eventName, index) => {
            if (eventName) {
               try {
                  const scanListener = DeviceEventEmitter.addListener(
                     eventName,
                     (e: any) => {
                        console.log(
                           `🔍 DEVICE DISCOVERED (listener ${index + 1}) - Event: ${eventName}`,
                           JSON.stringify(e, null, 2),
                        );

                        // Determine device type - could be PO3 or PO3M
                        let deviceType = String(e.type || "");
                        if (!deviceType) {
                           // Fallback logic based on device name or other properties
                           if (e.deviceName && e.deviceName.includes("PO3M")) {
                              deviceType = "PO3M";
                           } else {
                              deviceType = "PO3"; // Default assumption
                           }
                        }

                        const device: DeviceInfo = {
                           mac: String(e.mac || ""),
                           deviceType: deviceType,
                           deviceName: String(
                              e.deviceName || `iHealth ${deviceType} Scale`,
                           ),
                           protocolString: String(e.protocolstring || ""),
                        };

                        console.log("📱 Processed device info:", {
                           mac: device.mac,
                           type: device.deviceType,
                           name: device.deviceName,
                           protocol: device.protocolString,
                        });

                        this.notifyListeners("deviceDiscovered", device);
                     },
                  );
                  this.deviceEventListeners.push(scanListener);
                  listenerCount++;
                  console.log(
                     `✅ Event listener ${index + 1} registered for: ${eventName}`,
                  );
               } catch (error) {
                  console.log(
                     `❌ Failed to register listener ${index + 1} for: ${eventName}`,
                     error,
                  );
               }
            }
         });

         console.log(`🎧 Total event listeners registered: ${listenerCount}`);

         // ADDITIONAL: Try to listen to ALL possible events for debugging
         console.log("🧪 Setting up catch-all event listener for debugging...");
         try {
            const allEventsListener = DeviceEventEmitter.addListener(
               "iHealthDeviceManagerModule",
               (e: any) => {
                  console.log(
                     "🧪 CATCH-ALL EVENT:",
                     JSON.stringify(e, null, 2),
                  );
               },
            );
            this.deviceEventListeners.push(allEventsListener);
         } catch (error) {
            console.log("🧪 Catch-all listener failed:", error);
         }

         // Scan finish event
         if (iHealthDeviceManagerModule.Event_Scan_Finish) {
            const scanFinishListener = DeviceEventEmitter.addListener(
               iHealthDeviceManagerModule.Event_Scan_Finish,
               (e: any) => {
                  console.log("🏁 SCAN FINISHED:", JSON.stringify(e, null, 2));
               },
            );
            this.deviceEventListeners.push(scanFinishListener);
         }

         // Connection events
         if (iHealthDeviceManagerModule.Event_Device_Connected) {
            const connectedListener = DeviceEventEmitter.addListener(
               iHealthDeviceManagerModule.Event_Device_Connected,
               (e: any) => {
                  console.log(
                     "✅ DEVICE CONNECTED:",
                     JSON.stringify(e, null, 2),
                  );
                  this.connectionStatus = "connected";
                  const device: DeviceInfo = {
                     mac: String(e.mac || ""),
                     deviceType: String(e.type || "PO3"),
                     deviceName: String(
                        e.deviceName || "iHealth PO3M Pulse Oximeter",
                     ),
                     protocolString: String(e.protocolstring || ""),
                  };
                  this.connectedDevice = device;
                  this.notifyListeners("deviceConnected", device);
               },
            );
            this.deviceEventListeners.push(connectedListener);
         }

         if (iHealthDeviceManagerModule.Event_Device_Connect_Failed) {
            const connectFailedListener = DeviceEventEmitter.addListener(
               iHealthDeviceManagerModule.Event_Device_Connect_Failed,
               (e: any) => {
                  console.log(
                     "❌ CONNECTION FAILED:",
                     JSON.stringify(e, null, 2),
                  );
                  this.connectionStatus = "error";
                  this.notifyListeners("deviceError", e);
               },
            );
            this.deviceEventListeners.push(connectFailedListener);
         }

         if (iHealthDeviceManagerModule.Event_Device_Disconnect) {
            const disconnectedListener = DeviceEventEmitter.addListener(
               iHealthDeviceManagerModule.Event_Device_Disconnect,
               (e: any) => {
                  console.log(
                     "🔌 DEVICE DISCONNECTED:",
                     JSON.stringify(e, null, 2),
                  );
                  this.connectionStatus = "disconnected";
                  this.connectedDevice = null;
                  this.notifyListeners("deviceDisconnected", e);
               },
            );
            this.deviceEventListeners.push(disconnectedListener);
         }

         console.log("Event listeners setup complete");
      } catch (error) {
         console.error("Failed to setup event listeners:", error);
      }
   }

   /**
    * Start scanning - FOCUSED for Nexus Pro HS2S Pro
    */
   async startScan(): Promise<void> {
      try {
         if (!iHealthDeviceManagerModule) {
            throw new Error("iHealth module not available");
         }

         // Clear any existing timeout
         if (this.discoveryTimeout) {
            clearTimeout(this.discoveryTimeout);
            this.discoveryTimeout = null;
         }

         console.log("🔍 Starting iHealth PO3M pulse oximeter discovery...");
         console.log(
            "� CRITICAL: Make sure iHealth MyVitals app is COMPLETELY CLOSED!",
         );
         console.log("�📱 STEP ON YOUR SCALE NOW to activate it!");

         // CRITICAL FIX: Stop any existing discovery first
         console.log("🛑 Stopping any existing discovery first...");
         try {
            iHealthDeviceManagerModule.stopDiscovery();
            console.log("✅ Previous discovery stopped");
         } catch (error) {
            console.log("ℹ️ No previous discovery to stop");
         }

         // Wait before starting new discovery
         setTimeout(() => {
            console.log(
               "⏱️ 1 second delay completed, proceeding with discovery...",
            );
         }, 1000);

         if (typeof iHealthDeviceManagerModule.startDiscovery === "function") {
            console.log("🎯 PO3M PULSE OXIMETER - Focused Discovery");
            console.log("� Device: Nexus Pro HS2S Pro (from your image)");
            console.log("🔧 Expected SDK constant: PO3");

            // COMPREHENSIVE DEBUGGING: Check what's actually available
            console.log("📊 COMPREHENSIVE CONSTANT DEBUGGING:");
            console.log("  - PO3 (dot notation):", iHealthDeviceManagerModule.PO3);
            console.log("  - PO3M (dot notation):", iHealthDeviceManagerModule.PO3M);
            console.log("  - 'PO3' (bracket notation):", iHealthDeviceManagerModule['PO3']);
            console.log("  - 'PO3M' (bracket notation):", iHealthDeviceManagerModule['PO3M']);

            // Check if the module has a constantsToExport method or similar
            console.log("📋 ALL AVAILABLE CONSTANTS:");
            const allKeys = Object.keys(iHealthDeviceManagerModule);
            console.log("🔑 All module keys:", allKeys);

            // Filter for PO3 related keys
            const po3Keys = allKeys.filter(key => key.toLowerCase().includes('po3'));
            console.log("🫁 PO3-related keys:", po3Keys);

            // Check for any pulse oximeter related keys
            const pulseKeys = allKeys.filter(key =>
               key.toLowerCase().includes('pulse') ||
               key.toLowerCase().includes('oximeter') ||
               key.toLowerCase().includes('po')
            );
            console.log("🩺 Pulse/Oximeter related keys:", pulseKeys);

            // Check for constants that might be exported differently
            console.log("🔍 CHECKING FOR ALTERNATIVE CONSTANT EXPORTS:");
            console.log("  - kType_PO3:", iHealthDeviceManagerModule.kType_PO3);
            console.log("  - TYPE_PO3:", iHealthDeviceManagerModule.TYPE_PO3);
            console.log("  - PO3_TYPE:", iHealthDeviceManagerModule.PO3_TYPE);

            // Check if constants are exported as numbers
            console.log("🔢 CHECKING FOR NUMERIC CONSTANTS:");
            for (const key of allKeys) {
               const value = iHealthDeviceManagerModule[key];
               if (typeof value === 'number' || typeof value === 'string') {
                  console.log(`  - ${key}: ${value} (${typeof value})`);
               }
            }

            // PRIMARY ATTEMPT: Use PO3 constant (correct for PO3M pulse oximeter)
            if (iHealthDeviceManagerModule.PO3) {
               try {
                  console.log(
                     "🎯 PRIMARY: Starting PO3 discovery for PO3M pulse oximeter...",
                  );
                  const result = iHealthDeviceManagerModule.startDiscovery(
                     iHealthDeviceManagerModule.PO3,
                  );
                  console.log("🎯 PO3 discovery started:", result);
                  console.log(
                     "🎯 PO3 discovery started_____2:",
                     iHealthDeviceManagerModule.startDiscovery("PO3"),
                  );
                  const result2 = iHealthDeviceManagerModule.startDiscovery(
                     iHealthDeviceManagerModule.PO3M,
                  );
                  console.log("🔄 PO3M fallback started:", result2);
                  console.log(
                     "🎯 PO3M discovery started____2:",
                     iHealthDeviceManagerModule.startDiscovery("PO3M"),
                  );
                  // Give this the best chance with longer monitoring
                  setTimeout(() => {
                     console.log(
                        "🎯 PO3: 10 seconds elapsed - any PO3M devices found?",
                     );
                  }, 10000);
               } catch (error) {
                  console.log("❌ PO3 discovery failed:", error);
               }
            } else {
               console.log("❌ CRITICAL: PO3 constant not available!");
            }

            // FALLBACK 1: Try PO3M constant if PO3 doesn't work
            if (iHealthDeviceManagerModule.PO3M) {
               try {
                  console.log("🔄 FALLBACK 1: Trying PO3M constant...");
                  const result = iHealthDeviceManagerModule.startDiscovery(
                     iHealthDeviceManagerModule.PO3M,
                  );
                  console.log(
                     "🎯 PO3M discovery started____2:",
                     iHealthDeviceManagerModule.startDiscovery("PO3M"),
                  );
                  console.log("🔄 PO3M fallback started:", result);
               } catch (error) {
                  console.log("❌ PO3M fallback failed:", error);
               }
            }

            // FALLBACK 2: Try bracket notation PO3 if direct access doesn't work
            if (iHealthDeviceManagerModule["PO3"]) {
               try {
                  console.log("� FALLBACK: Trying HS2 for Nexus Pro...");
                  const result = iHealthDeviceManagerModule.startDiscovery(
                     iHealthDeviceManagerModule["PO3"],
                  );
                  console.log("� HS2 fallback started:", result);
               } catch (error) {
                  console.log("❌ HS2 fallback failed:", error);
               }
            }

            // FINAL ATTEMPT: Try discovery without parameters (scan all devices)
            try {
               console.log("🔍 FINAL: Scanning for all device types...");
               const result = iHealthDeviceManagerModule.startDiscovery();
               console.log("� All-device scan started:", result);
            } catch (error) {
               console.log("❌ All-device scan failed:", error);
            }

            // EXPERIMENTAL: Try using different parameter formats
            console.log("🧪 EXPERIMENTAL: Trying different parameter formats...");
            try {
               if (typeof iHealthDeviceManagerModule.startDiscovery === "function") {
                  // Try with different parameter formats that might work
                  const possiblePO3Values = ["PO3", "PO3M", "po3", "po3m"];
                  for (const value of possiblePO3Values) {
                     try {
                        console.log(`🧪 Trying discovery with string: "${value}"`);
                        const result = iHealthDeviceManagerModule.startDiscovery(value);
                        console.log(`✅ Discovery with "${value}" started:`, result);
                     } catch (err) {
                        console.log(`❌ Discovery with "${value}" failed:`, err);
                     }
                  }
               }
            } catch (error) {
               console.log("❌ Experimental discovery attempts failed:", error);
            }

            console.log(
               "✅ Device discovery started - scanning for 60 seconds...",
            );
            console.log(
               "🫁 KEEP YOUR PO3M PULSE OXIMETER ON to ensure it stays active!",
            );
            console.log("🎯 Listening for Event_Scan_Device events...");

            // Add periodic status checks
            let scanCount = 0;
            const statusInterval = setInterval(() => {
               scanCount++;
               if (scanCount <= 6) {
                  // Only run for 60 seconds (6 * 10 seconds)
                  console.log(
                     `🔄 Still scanning... (${scanCount * 10}s elapsed)`,
                  );
                  console.log(
                     "🫁 Make sure your PO3M PULSE OXIMETER is ON and ready!",
                  );
               } else {
                  clearInterval(statusInterval);
               }
            }, 10000);

            // Set timeout for discovery
            this.discoveryTimeout = setTimeout(() => {
               console.log("⏰ Discovery timeout reached - stopping scan");
               clearInterval(statusInterval);
               this.stopScan();
            }, 60000);
         } else {
            throw new Error("startDiscovery method not available");
         }
      } catch (error) {
         console.error("❌ Failed to start PO3M discovery:", error);
         this.connectionStatus = "error";
         throw error;
      }
   }

   /**
    * Stop scanning
    */
   async stopScan(): Promise<void> {
      try {
         // Clear timeout
         if (this.discoveryTimeout) {
            clearTimeout(this.discoveryTimeout);
            this.discoveryTimeout = null;
         }

         if (iHealthDeviceManagerModule?.stopDiscovery) {
            console.log("🛑 Stopping discovery...");
            iHealthDeviceManagerModule.stopDiscovery();
            console.log("✅ Discovery stopped");
         }
      } catch (error) {
         console.error("❌ Failed to stop discovery:", error);
      }
   }

   /**
    * Connect to device - use numeric device type constant
    */
   async connectDevice(mac: string, deviceType?: string): Promise<boolean> {
      try {
         if (!iHealthDeviceManagerModule) {
            throw new Error("iHealth module not available");
         }

         if (!mac || typeof mac !== "string") {
            throw new Error("Invalid MAC address");
         }

         this.connectionStatus = "connecting";
         console.log("🔗 Attempting to connect to iHealth device:", mac);
         console.log("🚨 CRITICAL: Ensure iHealth MyVitals app is CLOSED!");

         if (typeof iHealthDeviceManagerModule.connectDevice === "function") {
            // Try PO3 first, then PO3M if that fails
            console.log("📡 Trying connection with PO3 device type...");

            try {
               iHealthDeviceManagerModule.connectDevice(mac, "PO3");
               console.log(
                  "✅ PO3 connection request sent - waiting for response...",
               );
               console.log("⏳ This may take 10-30 seconds...");
               return true;
            } catch (po3Error) {
               console.log("🔄 PO3 connection failed, trying PO3M...");
               try {
                  iHealthDeviceManagerModule.connectDevice(mac, "PO3M");
                  console.log(
                     "✅ PO3M connection request sent - waiting for response...",
                  );
                  return true;
               } catch (po3mError) {
                  console.error(
                     "❌ Both PO3 and PO3M connection attempts failed",
                  );
                  throw po3mError;
               }
            }
         } else {
            throw new Error("connectDevice method not available");
         }
      } catch (error) {
         console.error("❌ Failed to connect device:", error);
         this.connectionStatus = "error";
         return false;
      }
   }

   /**
    * Disconnect device
    */
   async disconnectDevice(): Promise<void> {
      try {
         if (
            this.connectedDevice &&
            iHealthDeviceManagerModule?.disconnectDevice
         ) {
            console.log("🔌 Disconnecting device:", this.connectedDevice.mac);
            const HS2S_DEVICE_TYPE = "HS2S";
            iHealthDeviceManagerModule.disconnectDevice(
               this.connectedDevice.mac,
               HS2S_DEVICE_TYPE,
            );
            this.connectionStatus = "disconnected";
            this.connectedDevice = null;
            console.log("✅ Device disconnected");
         }
      } catch (error) {
         console.error("❌ Failed to disconnect device:", error);
      }
   }

   /**
    * Test device functionality - for HS2S Pro scales
    */
   async testDeviceFunctions(): Promise<void> {
      try {
         if (!this.connectedDevice) {
            throw new Error("No device connected");
         }

         console.log("🧪 Testing HS2S Pro device functions...");
         // Check if we have access to scale-specific modules
         const { HS2SModule } = NativeModules;
         if (HS2SModule) {
            console.log("📊 HS2S Module available:", Object.keys(HS2SModule));
            // Listen for measurement events
            if (HS2SModule.Event_Notify) {
               const measurementListener = DeviceEventEmitter.addListener(
                  HS2SModule.Event_Notify,
                  (e: any) => {
                     console.log(
                        "📏 MEASUREMENT RECEIVED:",
                        JSON.stringify(e, null, 2),
                     );
                     this.notifyListeners("measurementReceived", e);
                  },
               );
               this.deviceEventListeners.push(measurementListener);
            }

            // Try to start measurement if method is available
            if (HS2SModule.startMeasure && this.connectedDevice) {
               console.log(
                  "🎯 Starting measurement on device:",
                  this.connectedDevice.mac,
               );
               HS2SModule.startMeasure(this.connectedDevice.mac);
            }
         } else {
            console.log(
               "ℹ️ HS2S Module not available - device may need to send data by stepping on scale",
            );
         }

         console.log("✅ Device function test completed");
      } catch (error) {
         console.error("❌ Device function test failed:", error);
      }
   }

   /**
    * Get current connection status
    */
   getConnectionStatus(): DeviceConnectionStatus {
      return this.connectionStatus;
   }

   /**
    * Get connected device info
    */
   getConnectedDevice(): DeviceInfo | null {
      return this.connectedDevice;
   }

   /**
    * Add event listener
    */
   addEventListener(event: string, callback: (data: any) => void) {
      if (!this.listeners[event]) {
         this.listeners[event] = [];
      }
      this.listeners[event].push(callback);
   }

   /**
    * Remove event listener
    */
   removeEventListener(event: string, callback: (data: any) => void) {
      if (this.listeners[event]) {
         this.listeners[event] = this.listeners[event].filter(
            (cb) => cb !== callback,
         );
      }
   }

   /**
    * Notify all listeners for an event
    */
   private notifyListeners(event: string, data: any) {
      if (this.listeners[event]) {
         this.listeners[event].forEach((callback) => callback(data));
      }
   }

   /**
    * Cleanup resources
    */
   cleanup() {
      try {
         console.log("🧹 Cleaning up iHealth service...");
         // Clear discovery timeout
         if (this.discoveryTimeout) {
            clearTimeout(this.discoveryTimeout);
            this.discoveryTimeout = null;
         }

         this.deviceEventListeners.forEach((listener) => {
            if (listener?.remove) {
               listener.remove();
            }
         });
         this.deviceEventListeners = [];
         this.listeners = {};
         this.connectedDevice = null;
         this.connectionStatus = "disconnected";
         console.log("✅ Cleanup complete");
      } catch (error) {
         console.error("❌ Error during cleanup:", error);
      }
   }
}

// Export singleton instance
export const iHealthService = new IHealthService();
export default iHealthService;
