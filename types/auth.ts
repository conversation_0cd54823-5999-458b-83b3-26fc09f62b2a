export type Gender = "MALE" | "FEMALE" | "OTHER";

export interface LoginRequest {
   email: string;
   password: string;
}

export interface SignUpRequest {
   firstName: string;
   lastName: string;
   email: string;
   password: string;
   dob: string;
   gender: Gender;
}

export interface LoginResponse {
   access_token: string;
   refresh_token: string;
}

export interface BackendUser {
   id: number;
   firstName: string;
   lastName: string;
   email: string;
   active: boolean;
   verified: boolean;
   role: string;
   dob: string;
   gender: Gender;
}

export type SignUpResponse = BackendUser;
